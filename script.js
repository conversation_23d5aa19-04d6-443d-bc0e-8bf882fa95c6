// Audio for timer
let timerAudio = new Audio('audio/clock_timer.mp3');
// No loop - play only once
timerAudio.loop = false; // Explicitly set to false to ensure it doesn't loop

// Add an event listener to handle when the audio ends
timerAudio.addEventListener('ended', function() {
    console.log('Timer audio finished playing');
    // The audio has finished playing naturally
    // No need to do anything here as we want it to play fully once
});

// Audio for fireworks (confetti sound)
const fireworksAudio = new Audio('audio/fireworks.mp3');
fireworksAudio.loop = false;

// Audio for correct answer
const correctAudio = new Audio('audio/correct.mp3');
correctAudio.loop = false;

// Audio for woosh
const wooshAudio = new Audio('audio/woosh.mp3');
wooshAudio.loop = false;

// Audio for wrong answer
const wrongAudio = new Audio('audio/wrong.mp3');
wrongAudio.loop = false;

// Audio for button hover
const hoverAudio = new Audio('audio/hover_button.mp3');
hoverAudio.loop = false;
hoverAudio.volume = 0.5; // Set volume to 50% to make it less intrusive

// Audio for background music
const backgroundMusic = new Audio('audio/background_music.mp3');
backgroundMusic.loop = true; // Enable looping
backgroundMusic.volume = 0.3; // Set volume to 30% to keep it in the background

// Function to play background music
function playBackgroundMusic() {
    // Reset the audio to the beginning
    backgroundMusic.currentTime = 0;
    // Play the background music
    backgroundMusic.play().catch(error => {
        console.log('Error playing background music:', error);
    });
}

// Function to stop background music
function stopBackgroundMusic() {
    backgroundMusic.pause();
    backgroundMusic.currentTime = 0;
}

// Data Storage
let rounds = [
    {
        name: "Bible Basics",
        type: "questions", // New property to distinguish round types
        questions: [
            {
                text: "Who was the first man created according to the Bible?",
                options: ["Adam", "Noah", "Abraham", "Moses"],
                correctOption: 0
            },
            {
                text: "How many days did it take God to create the world according to Genesis?",
                options: ["3 days", "6 days", "7 days", "40 days"],
                correctOption: 1
            },
            {
                text: "Who built the ark according to the Bible?",
                options: ["Moses", "Abraham", "Noah", "David"],
                correctOption: 2
            },
            {
                text: "Which of these is NOT one of the Ten Commandments?",
                options: ["Do not steal", "Love your neighbor as yourself", "Do not commit adultery", "Do not bear false witness"],
                correctOption: 1
            },
            {
                text: "Who led the Israelites out of Egypt?",
                options: ["Joshua", "Moses", "Abraham", "David"],
                correctOption: 1
            }
        ]
    },
    {
        name: "Anagram Challenge",
        type: "anagrams", // New anagram round type
        anagrams: [
            {
                word: "DEKAPOLIS",
                hintLetters: {2: "E", 5: "O", 7: "L", 9: "S"}, // Position: Letter (1-based indexing)
                randomLetters: ["D", "K", "P", "A", "I"],
                correctSequence: ["D", "K", "P", "A", "I"], // Order in which random letters should be clicked
                availableLettersOrder: ["P", "I", "A", "D", "K"] // Custom order to display available letters (scrambled to prevent easy guessing)
            }
        ]
    }
];

// Flatten questions for backward compatibility with existing code
let questions = [];
rounds.forEach(round => {
    if (round.questions) {
        round.questions.forEach(question => {
            questions.push(question);
        });
    }
});

let teams = [];
let profileImages = []; // Array to store profile image paths
let questionImages = []; // Array to store question image paths
let participantNames = []; // Array to store participant names for dropdown
let displayedScores = []; // Store the current displayed scores to track changes
let scoresNeedUpdate = false;
// Quiz state tracking
let quizInProgress = false;
let quizPaused = false;
let currentSettings = {
    quizTitle: 'Bible Quiz Competition',
    timerDuration: 30,
    pointsPerCorrectAnswer: 1,
    shuffleQuestions: true,
    titleAlignment: 'left',
    titleSize: 'medium',
    questionAlignment: 'left',
    questionSize: '1.5',
    timerSize: '1'
};

let currentQuizState = {
    currentQuestionIndex: 0,
    currentTeamIndex: 0,
    currentRoundIndex: 0,
    currentRoundQuestionNumber: 0, // Tracks the current question number within the round (1-based)
    score: [],
    timer: null,
    timeLeft: 30,
    questionAnswered: false,
    timerPaused: false,
    teamQuestions: [], // Tracks which questions have been shown to each team
    availableQuestions: [], // Pool of questions not yet shown to current team
    usedQuestions: [], // Global tracking of all questions that have been used in the quiz
    roundsCompleted: false, // Flag to indicate if all rounds are completed
    completedRounds: [], // Array of indices of completed rounds
    roundQuestions: [], // Questions for the current round
    // Anagram-specific state
    currentRoundType: "questions", // "questions" or "anagrams"
    anagramState: {
        currentAnagramIndex: 0,
        clickedLetters: [], // Letters clicked in order
        correctSequence: [], // The correct sequence for current anagram
        anagramCompleted: false,
        firstAttempt: true // Track if this is the first attempt for scoring
    }
};

// DOM Elements
// Menu elements
const menuSection = document.getElementById('menuSection');
const goToTeamsBtn = document.getElementById('goToTeamsBtn');
const goToRoundsBtn = document.getElementById('goToRoundsBtn');
const goToScoresBtn = document.getElementById('goToScoresBtn');
const goToSettingsBtn = document.getElementById('goToSettingsBtn');
const startQuizBtn = document.getElementById('startQuizBtn');
const continueQuizBtn = document.getElementById('continueQuizBtn');
const restartQuizBtn = document.getElementById('restartQuizBtn');
const exportBackupBtn = document.getElementById('exportBackupBtn');
const importBackupBtn = document.getElementById('importBackupBtn');
const backupFileInput = document.getElementById('backupFileInput');
const fabButton = document.getElementById('fabButton');
const fabMenu = document.getElementById('fabMenu');

// Teams section elements
const teamsSection = document.querySelector('.teams-section');
const backToMenuFromTeamsBtn = document.getElementById('backToMenuFromTeamsBtn');
const addTeamBtn = document.getElementById('addTeamBtn');
const teamsContainer = document.getElementById('teamsContainer');

// Rounds section elements
const roundSelectionSection = document.getElementById('roundSelectionSection');
const roundsSelectionContainer = document.getElementById('roundsSelectionContainer');
const backToMenuFromRoundsBtn = document.getElementById('backToMenuFromRoundsBtn');

// Scores section elements
const scoresSection = document.getElementById('scoresSection');
const scoresContainer = document.getElementById('scoresContainer');
const roundsSummary = document.getElementById('roundsSummary');
const backToMenuFromScoresBtn = document.getElementById('backToMenuFromScoresBtn');

// Quiz section elements
const quizSection = document.getElementById('quizSection');
const backToMenuFromQuizBtn = document.getElementById('backToMenuFromQuizBtn');
const resultsSection = document.getElementById('resultsSection');
const currentQuestionEl = document.getElementById('currentQuestion');
const totalQuestionsEl = document.getElementById('totalQuestions');
const timerEl = document.getElementById('timer');
const currentTeamEl = document.getElementById('currentTeamBox');
const currentTeamScoreEl = document.getElementById('currentTeamScore');
const currentRoundEl = document.getElementById('currentRoundBox');
const questionTextEl = document.getElementById('questionText');
const optionsContainerEl = document.getElementById('optionsContainer');
const startTimerBtn = document.getElementById('startTimerBtn');
const nextQuestionBtn = document.getElementById('nextQuestionBtn');
const selectNextRoundBtn = document.getElementById('selectNextRoundBtn');
const newQuizBtn = document.getElementById('newQuizBtn');
const resultsContainerEl = document.getElementById('resultsContainer');
const roundsList = document.getElementById('roundsList');
const addRoundBtn = document.getElementById('addRoundBtn');
const questionRoundSelect = document.getElementById('questionRoundSelect');

// Settings Modal Elements
const settingsModal = document.getElementById('settingsModal');
const closeSettingsBtn = document.getElementById('closeSettingsBtn');
const tabBtns = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');
const questionsList = document.getElementById('questionsList');
const addQuestionBtn = document.getElementById('addQuestionBtn');
const settingsTeamsList = document.getElementById('settingsTeamsList');
const settingsAddTeamBtn = document.getElementById('settingsAddTeamBtn');
const quizTitleInput = document.getElementById('quizTitle');
const timerDurationInput = document.getElementById('timerDuration');
const shuffleQuestionsInput = document.getElementById('shuffleQuestions');
const titleAlignmentInput = document.getElementById('titleAlignment');
const titleSizeInput = document.getElementById('titleSize');
const questionAlignmentInput = document.getElementById('questionAlignment');
const questionSizeInput = document.getElementById('questionSize');
const timerSizeInput = document.getElementById('timerSize');
const saveSettingsBtn = document.getElementById('saveSettingsBtn');

// Add Question Modal Elements
const addQuestionModal = document.getElementById('addQuestionModal');
const closeQuestionModalBtn = document.getElementById('closeQuestionModalBtn');
const questionModalTitle = document.getElementById('questionModalTitle');
const questionInput = document.getElementById('questionInput');
const option1Input = document.getElementById('option1Input');
const option2Input = document.getElementById('option2Input');
const option3Input = document.getElementById('option3Input');
const option4Input = document.getElementById('option4Input');
const saveQuestionBtn = document.getElementById('saveQuestionBtn');

// Add Team Modal Elements
const addTeamModal = document.getElementById('addTeamModal');
const closeTeamModalBtn = document.getElementById('closeTeamModalBtn');
const teamNameInput = document.getElementById('teamNameInput');
const teamBackgroundColorInput = document.getElementById('teamBackgroundColorInput'); // New element
const participantsContainer = document.getElementById('participantsContainer');
const addParticipantBtn = document.getElementById('addParticipantBtn');
const saveTeamBtn = document.getElementById('saveTeamBtn');

// Add Round Modal Elements
const addRoundModal = document.getElementById('addRoundModal');
const closeRoundModalBtn = document.getElementById('closeRoundModalBtn');
const roundModalTitle = document.getElementById('roundModalTitle');
const roundNameInput = document.getElementById('roundNameInput');
const saveRoundBtn = document.getElementById('saveRoundBtn');

// Get current round's timer settings
function getCurrentRoundTimerSettings() {
    if (currentQuizState.currentRoundIndex >= 0 && currentQuizState.currentRoundIndex < rounds.length) {
        const currentRound = rounds[currentQuizState.currentRoundIndex];
        return {
            duration: currentRound.timerDuration || currentSettings.timerDuration,
            sound: currentRound.timerSound || 'clock_timer.mp3'
        };
    }
    // Fallback to global settings
    return {
        duration: currentSettings.timerDuration,
        sound: 'clock_timer.mp3'
    };
}

// Update timer audio based on current round settings
function updateTimerAudio() {
    const timerSettings = getCurrentRoundTimerSettings();
    const newAudioPath = 'audio/' + timerSettings.sound;

    // Only update if the audio source has changed
    if (timerAudio.src !== new URL(newAudioPath, window.location.href).href) {
        timerAudio.src = newAudioPath;
        timerAudio.load(); // Reload the audio element
    }
}

// Migrate existing rounds to include timer settings
function migrateRoundsData() {
    let needsSave = false;

    rounds.forEach(round => {
        // Add timer duration if it doesn't exist
        if (typeof round.timerDuration === 'undefined') {
            round.timerDuration = 30; // Default timer duration
            needsSave = true;
        }

        // Add timer sound if it doesn't exist
        if (typeof round.timerSound === 'undefined') {
            round.timerSound = 'clock_timer.mp3'; // Default timer sound
            needsSave = true;
        }

        // Add type if it doesn't exist (for very old rounds)
        if (typeof round.type === 'undefined') {
            round.type = round.anagrams ? 'anagrams' : 'questions';
            needsSave = true;
        }
    });

    // Save the migrated data if changes were made
    if (needsSave) {
        console.log('Migrated rounds data to include timer settings');
        saveData();
    }
}

// Load audio files from the audio folder for timer sound selection
function loadAudioFiles() {
    try {
        // List of known audio files in the audio folder
        const audioFiles = [
            'clock_30sec.mp3',
            'clock_40sec.mp3',
        ];

        // Populate the timer sound dropdown
        const roundTimerSoundSelect = document.getElementById('roundTimerSound');
        if (roundTimerSoundSelect) {
            roundTimerSoundSelect.innerHTML = '';

            audioFiles.forEach(audioFile => {
                const option = document.createElement('option');
                option.value = audioFile;
                option.textContent = audioFile.replace('.mp3', '').replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                roundTimerSoundSelect.appendChild(option);
            });

            // Set default selection to clock_timer.mp3
            roundTimerSoundSelect.value = 'clock_timer.mp3';
        }

        console.log('Loaded audio files for timer selection:', audioFiles);
    } catch (error) {
        console.error('Error loading audio files:', error);
    }
}

// Initialize the app
function init() {
    loadProfileImages(); // Load profile image paths
    loadQuestionImages(); // Load question image paths
    loadParticipantNames(); // Load participant names
    loadAudioFiles(); // Load audio files for timer sound selection
    loadData();

    // Add sample teams for testing if no teams exist
    if (teams.length === 0) {
        addSampleTeams();
    }

    // Initialize displayed scores
    if (!displayedScores || displayedScores.length !== teams.length) {
        displayedScores = teams.map(() => 0);
    }

    renderTeams();
    renderRoundsList();
    updateRoundSelect();
    renderQuestionsList();
    renderSettingsTeamsList();
    updateSettingsForm();

    // Apply the saved title to the header
    const titleElement = document.querySelector('header h1');
    if (titleElement && currentSettings.quizTitle) {
        titleElement.textContent = currentSettings.quizTitle;
    }

    // Apply styles
    applyTitleStyles();
    applyQuestionStyles();
    applyTimerStyles();

    // Initialize floating action button
    initFloatingActionButton();

    // Apply hover sound to all buttons
    applyHoverSoundToButtons();

    // Always show the menu section by default when the app starts
    showMenu();

    // Add main navigation event listeners
    if (goToTeamsBtn) goToTeamsBtn.addEventListener('click', showTeamsSection);
    if (goToRoundsBtn) goToRoundsBtn.addEventListener('click', showRoundsSection);
    if (goToScoresBtn) goToScoresBtn.addEventListener('click', showScoresSection);
    if (goToSettingsBtn) goToSettingsBtn.addEventListener('click', () => settingsModal.classList.add('active'));
    if (startQuizBtn) startQuizBtn.addEventListener('click', showRoundsSection); // Start quiz now shows rounds selection
    if (continueQuizBtn) continueQuizBtn.addEventListener('click', continueQuiz);
    if (restartQuizBtn) restartQuizBtn.addEventListener('click', restartQuiz);
    if (exportBackupBtn) exportBackupBtn.addEventListener('click', exportBackup);
    if (backToMenuFromTeamsBtn) backToMenuFromTeamsBtn.addEventListener('click', showMenu);
    if (backToMenuFromRoundsBtn) backToMenuFromRoundsBtn.addEventListener('click', showMenu);
    if (backToMenuFromScoresBtn) backToMenuFromScoresBtn.addEventListener('click', showMenu);

    // Add event listener for start timer button
    if (startTimerBtn) {
        startTimerBtn.addEventListener('click', function() {
            // First show options, then start timer
            showOptionsAndStartTimer();
        });
    }

    // Add event listener for title size slider
    if (titleSizeInput) {
        titleSizeInput.addEventListener('input', updateSliderValueDisplay);
    }

    // Add event listener for question size slider
    if (questionSizeInput) {
        questionSizeInput.addEventListener('input', updateQuestionSliderValueDisplay);
    }

    // Add event listener for timer size slider
    if (timerSizeInput) {
        timerSizeInput.addEventListener('input', updateTimerSliderValueDisplay);
    }

    // Add event listener for round selection change
    if (questionRoundSelect) {
        questionRoundSelect.addEventListener('change', function() {
            renderQuestionsList();
        });
    }

    // Add event listener for add round button
    if (addRoundBtn) {
        addRoundBtn.addEventListener('click', openAddRoundModal);
    }

    // Add event listener for close round modal button
    if (closeRoundModalBtn) {
        closeRoundModalBtn.addEventListener('click', () => addRoundModal.classList.remove('active'));
    }

    // Add event listener for save round button
    if (saveRoundBtn) {
        saveRoundBtn.addEventListener('click', saveRound);
    }

    // Add event listener for import backup button
    if (importBackupBtn) {
        importBackupBtn.addEventListener('click', function() {
            // Trigger the hidden file input
            backupFileInput.click();
        });
    }

    // Add event listener for file input change
    if (backupFileInput) {
        backupFileInput.addEventListener('change', importBackup);
    }

    // Add other essential event listeners
    if (addTeamBtn) addTeamBtn.addEventListener('click', openAddTeamModal);
    if (selectNextRoundBtn) selectNextRoundBtn.addEventListener('click', goToRoundSelection);
    if (newQuizBtn) newQuizBtn.addEventListener('click', newQuiz);

    // Quiz navigation
    if (backToMenuFromQuizBtn) {
        backToMenuFromQuizBtn.addEventListener('click', function() {
            // Pause the quiz and go to menu
            if (currentQuizState.timer) {
                clearInterval(currentQuizState.timer);
            }

            // Only stop the timer audio if we're going to menu before it finishes naturally
            if (!timerAudio.ended) {
                timerAudio.pause();
                timerAudio.currentTime = 0;
            }

            quizPaused = true;
            showMenu();
        });
    }

    // Scores update button
    const updateScoresBtn = document.getElementById('updateScoresBtn');
    if (updateScoresBtn) {
        updateScoresBtn.addEventListener('click', updateScoresWithAnimation);
    }

    // Settings modal event listeners
    if (closeSettingsBtn) closeSettingsBtn.addEventListener('click', () => settingsModal.classList.remove('active'));
    if (tabBtns) {
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                btn.classList.add('active');
                document.getElementById(`${btn.dataset.tab}Tab`).classList.add('active');

                // If switching to scores tab, render the score management interface
                if (btn.dataset.tab === 'scores') {
                    renderScoreManagement();
                }
            });
        });
    }

    // Score management event listeners
    const resetAllScoresBtn = document.getElementById('resetAllScoresBtn');
    const saveScoresBtn = document.getElementById('saveScoresBtn');

    if (resetAllScoresBtn) {
        resetAllScoresBtn.addEventListener('click', resetAllScores);
    }

    if (saveScoresBtn) {
        saveScoresBtn.addEventListener('click', saveScoreChanges);
    }
    if (addQuestionBtn) addQuestionBtn.addEventListener('click', openAddQuestionModal);
    if (settingsAddTeamBtn) settingsAddTeamBtn.addEventListener('click', openAddTeamModal);
    if (saveSettingsBtn) saveSettingsBtn.addEventListener('click', saveSettings);

    // Question modal event listeners
    if (closeQuestionModalBtn) closeQuestionModalBtn.addEventListener('click', () => addQuestionModal.classList.remove('active'));
    if (saveQuestionBtn) saveQuestionBtn.addEventListener('click', saveQuestion);

    // Team modal event listeners
    if (closeTeamModalBtn) closeTeamModalBtn.addEventListener('click', () => addTeamModal.classList.remove('active'));
    if (addParticipantBtn) addParticipantBtn.addEventListener('click', addParticipantInput);
    if (saveTeamBtn) saveTeamBtn.addEventListener('click', saveTeam);

    // Anagram modal event listeners
    const addAnagramBtn = document.getElementById('addAnagramBtn');
    const closeAnagramModalBtn = document.getElementById('closeAnagramModalBtn');
    const saveAnagramBtn = document.getElementById('saveAnagramBtn');

    if (addAnagramBtn) addAnagramBtn.addEventListener('click', openAddAnagramModal);
    if (closeAnagramModalBtn) {
        closeAnagramModalBtn.addEventListener('click', () => {
            document.getElementById('addAnagramModal').classList.remove('active');
        });
    }
    if (saveAnagramBtn) saveAnagramBtn.addEventListener('click', saveAnagram);

    // Initialize question image dropdown when the modal is opened
    if (addQuestionBtn) {
        addQuestionBtn.addEventListener('click', () => {
            setTimeout(() => {
                populateQuestionImageDropdown();
                // Add event listener for image selection change
                const dropdown = document.getElementById('questionImageSelect');
                if (dropdown) {
                    dropdown.addEventListener('change', updateQuestionImagePreview);
                }
            }, 100);
        });
    }
}

// Update the slider value display
function updateSliderValueDisplay() {
    const sliderValue = document.querySelector('.slider-value');
    if (sliderValue && titleSizeInput) {
        const value = parseFloat(titleSizeInput.value);
        let sizeText = 'Medium';

        if (value <= 1.5) {
            sizeText = 'Small';
        } else if (value >= 2.5) {
            sizeText = 'Large';
        }

        sliderValue.textContent = sizeText;
    }
}

// Update the question slider value display
function updateQuestionSliderValueDisplay() {
    const sliderValue = document.querySelector('.slider-value-question');
    if (sliderValue && questionSizeInput) {
        const value = parseFloat(questionSizeInput.value);
        let sizeText = 'Medium';

        if (value <= 1.2) {
            sizeText = 'Small';
        } else if (value >= 1.8 && value < 2.5) {
            sizeText = 'Large';
        } else if (value >= 2.5) {
            sizeText = 'Extra Large';
        }

        sliderValue.textContent = sizeText;
    }
}

// Update the timer slider value display
function updateTimerSliderValueDisplay() {
    const sliderValue = document.querySelector('.slider-value-timer');
    if (sliderValue && timerSizeInput) {
        const value = parseFloat(timerSizeInput.value);
        let sizeText = 'Medium';

        if (value <= 0.9) {
            sizeText = 'Small';
        } else if (value >= 1.3) {
            sizeText = 'Large';
        }

        sliderValue.textContent = sizeText;

        // Apply the timer size immediately for preview
        applyTimerStyles();
    }
}

// Apply title styles based on settings
function applyTitleStyles() {
    const titleElement = document.querySelector('header h1');
    if (titleElement) {
        // Apply alignment
        titleElement.style.textAlign = currentSettings.titleAlignment || 'left';

        // Apply size - now using a numeric value from the slider
        const sizeValue = parseFloat(currentSettings.titleSize) || 2;
        const fontSize = sizeValue + 'rem';
        titleElement.style.fontSize = fontSize;
    }
}

// Apply question styles based on settings
function applyQuestionStyles() {
    if (questionTextEl) {
        // Apply alignment
        questionTextEl.style.textAlign = currentSettings.questionAlignment || 'left';

        // Apply size - using a numeric value from the slider
        const sizeValue = parseFloat(currentSettings.questionSize) || 1.5;
        const fontSize = sizeValue + 'rem';
        questionTextEl.style.fontSize = fontSize;
    }
}

// Apply timer styles based on settings
function applyTimerStyles() {
    const timerCircle = document.querySelector('.timer-circle');
    const timerText = document.querySelector('.timer-text');
    const quizHeader = document.querySelector('.quiz-header');

    if (timerCircle && timerText && quizHeader) {
        // Get the size value from settings or slider
        const sizeValue = parseFloat(currentSettings.timerSize || (timerSizeInput ? timerSizeInput.value : 1));

        // Apply size to timer circle
        const circleSize = 100 * sizeValue;
        timerCircle.style.width = circleSize + 'px';
        timerCircle.style.height = circleSize + 'px';

        // Apply size to timer text
        const fontSize = 2.5 * sizeValue;
        timerText.style.fontSize = fontSize + 'rem';

        // Adjust quiz header height based on timer size to prevent overlap
        const minHeight = Math.max(150, circleSize + 50);
        quizHeader.style.minHeight = minHeight + 'px';

        // Adjust margin bottom based on timer size
        const marginBottom = Math.max(30, circleSize / 2);
        quizHeader.style.marginBottom = marginBottom + 'px';
    }
}

// Initialize floating action button
function initFloatingActionButton() {
    if (fabButton && fabMenu) {
        // Toggle the fab menu when the button is clicked
        fabButton.addEventListener('click', function() {
            fabMenu.classList.toggle('hidden');

            // Add animation class to the button
            fabButton.classList.toggle('active');

            // If the menu is now visible, add a click outside listener
            if (!fabMenu.classList.contains('hidden')) {
                setTimeout(() => {
                    document.addEventListener('click', closeMenuOnClickOutside);
                }, 10);
            }
        });

        // Prevent clicks on the menu from closing it
        fabMenu.addEventListener('click', function(event) {
            event.stopPropagation();
        });
    }
}

// Close the menu when clicking outside
function closeMenuOnClickOutside(event) {
    // If the click is not on the fab button and the menu is visible
    if (fabButton && fabMenu && !fabButton.contains(event.target) && !fabMenu.contains(event.target)) {
        fabMenu.classList.add('hidden');
        fabButton.classList.remove('active');
        document.removeEventListener('click', closeMenuOnClickOutside);
    }
}

// Apply hover sound to all buttons
function applyHoverSoundToButtons() {
    // Track if user has interacted with the page
    let userHasInteracted = false;

    // Listen for first user interaction
    const enableAudio = () => {
        userHasInteracted = true;
        document.removeEventListener('click', enableAudio);
        document.removeEventListener('keydown', enableAudio);
    };

    document.addEventListener('click', enableAudio);
    document.addEventListener('keydown', enableAudio);

    // Create a function to play the hover sound
    const playHoverSound = () => {
        if (!userHasInteracted) return; // Don't play if user hasn't interacted yet

        // Reset the audio to the beginning to allow rapid hovering
        hoverAudio.currentTime = 0;
        // Play the hover sound
        hoverAudio.play().catch(error => {
            // Silently catch errors (common with rapid hovering)
            console.error('Error playing hover sound:', error);
        });
    };

    // Function to apply hover sound to a button
    const addHoverSoundToButton = (button) => {
        button.addEventListener('mouseenter', playHoverSound);
    };

    // Apply to all buttons with class 'btn'
    document.querySelectorAll('.btn').forEach(addHoverSoundToButton);

    // Apply to all menu buttons
    document.querySelectorAll('.menu-button').forEach(addHoverSoundToButton);

    // Apply to all fab menu items
    document.querySelectorAll('.fab-item').forEach(addHoverSoundToButton);

    // Apply to the fab button itself
    document.querySelectorAll('.fab-button').forEach(addHoverSoundToButton);

    // Apply to all option elements (answer options)
    document.querySelectorAll('.option').forEach(addHoverSoundToButton);

    // Apply to all round cards
    document.querySelectorAll('.round-card').forEach(addHoverSoundToButton);

    // Set up a mutation observer to apply hover sound to new buttons added to the DOM
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach((node) => {
                    // Check if the added node is an element
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Apply to the node itself if it's a button
                        if (node.classList && (node.classList.contains('btn') ||
                                              node.classList.contains('option') ||
                                              node.classList.contains('round-card') ||
                                              node.classList.contains('menu-button') ||
                                              node.classList.contains('fab-item') ||
                                              node.classList.contains('fab-button'))) {
                            addHoverSoundToButton(node);
                        }

                        // Apply to any buttons inside the added node
                        node.querySelectorAll('.btn, .option, .round-card, .menu-button, .fab-item, .fab-button').forEach(addHoverSoundToButton);
                    }
                });
            }
        });
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
}

// Add sample teams for testing
function addSampleTeams() {
    const sampleTeams = [
        {
            name: "Jasper",
            backgroundColor: "linear-gradient(145deg, #4CAF50, #45a049)",
            participants: [
                { name: "Htebdk dpys", profileImage: "APC Military Police Vehicle  Physics pack controller  available on the asset store 3.webp" },
                { name: "Lhdkdk jdhd", profileImage: "APC Military Police Vehicle  Physics pack controller  available on the asset store 25.webp" }
            ]
        },
        {
            name: "Sapphire",
            backgroundColor: "linear-gradient(145deg, #2196F3, #1976D2)",
            participants: [
                { name: "Ubhdjhdp Hfjydu", profileImage: "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 04.webp" },
                { name: "Thndkdi Usadg", profileImage: "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 07.webp" }
            ]
        },
        {
            name: "Beryl",
            backgroundColor: "linear-gradient(145deg, #FF9800, #F57C00)",
            participants: [
                { name: "Rkdhdi ljdkdi", profileImage: "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 09.webp" },
                { name: "Ljhdjj Gldgkdl", profileImage: "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 13.webp" }
            ]
        }
    ];

    teams.push(...sampleTeams);

    // Add some sample scores for testing
    if (!currentQuizState.score || currentQuizState.score.length === 0) {
        currentQuizState.score = [5, 12, 8]; // Jasper: 5, Sapphire: 12, Beryl: 8
        displayedScores = [5, 8, 12]; // Different displayed scores to show animation
    }

    saveData();
}

// Load data from localStorage
function loadData() {
    const savedRounds = localStorage.getItem('bibleQuizRounds');
    const savedTeams = localStorage.getItem('bibleQuizTeams');
    const savedSettings = localStorage.getItem('bibleQuizSettings');
    const savedQuizState = localStorage.getItem('bibleQuizState');
    const savedQuizProgress = localStorage.getItem('bibleQuizProgress');
    const savedParticipantNames = localStorage.getItem('bibleQuizParticipantNames');

    if (savedRounds) {
        rounds = JSON.parse(savedRounds);
        // Migrate existing rounds to include timer settings if they don't have them
        migrateRoundsData();
        // Update flattened questions array
        updateFlattenedQuestions();
    }

    if (savedTeams) {
        teams = JSON.parse(savedTeams);
    }

    if (savedParticipantNames) {
        participantNames = JSON.parse(savedParticipantNames);
    }

    if (savedSettings) {
        currentSettings = JSON.parse(savedSettings);
    }

    // Load quiz state if it exists
    if (savedQuizState) {
        const loadedState = JSON.parse(savedQuizState);

        // Copy all properties from loadedState to currentQuizState
        for (const key in loadedState) {
            if (key !== 'timer') { // Don't restore the timer reference
                currentQuizState[key] = loadedState[key];
            }
        }

        // Make sure we have a valid score array
        if (!currentQuizState.score || currentQuizState.score.length !== teams.length) {
            currentQuizState.score = teams.map(() => 0);
        }

        // Make sure we have valid team questions arrays
        if (!currentQuizState.teamQuestions || currentQuizState.teamQuestions.length !== teams.length) {
            currentQuizState.teamQuestions = teams.map(() => []);
        }
    }

    // Load quiz progress flags
    if (savedQuizProgress) {
        const progress = JSON.parse(savedQuizProgress);
        quizInProgress = progress.inProgress || false;
        quizPaused = progress.paused || false;
    }
}

// Save data to localStorage
function saveData() {
    localStorage.setItem('bibleQuizRounds', JSON.stringify(rounds));
    localStorage.setItem('bibleQuizTeams', JSON.stringify(teams));
    localStorage.setItem('bibleQuizSettings', JSON.stringify(currentSettings));
    localStorage.setItem('bibleQuizParticipantNames', JSON.stringify(participantNames));

    // Update flattened questions array
    updateFlattenedQuestions();
}

// Save current quiz state to localStorage
function saveQuizState() {
    // Create a copy of the current state without the timer reference
    const stateToSave = { ...currentQuizState };
    delete stateToSave.timer; // Remove the timer reference as it can't be serialized

    // Save the state
    localStorage.setItem('bibleQuizState', JSON.stringify(stateToSave));

    // Save quiz progress flags
    const progress = {
        inProgress: quizInProgress,
        paused: quizPaused
    };
    localStorage.setItem('bibleQuizProgress', JSON.stringify(progress));
}

// Update the flattened questions array from rounds
function updateFlattenedQuestions() {
    questions = [];
    if (rounds && Array.isArray(rounds)) {
        rounds.forEach(round => {
            if (round && round.questions) {
                round.questions.forEach(question => {
                    questions.push(question);
                });
            }
        });
    }
}

// Export backup of quiz settings, rounds, questions, and teams
function exportBackup() {
    // Create a backup object with all the data we want to save
    const backupData = {
        rounds: rounds,
        teams: teams,
        settings: currentSettings,
        participantNames: participantNames, // Include participant names in the backup
        questionImages: questionImages, // Include question images in the backup
        version: '1.1', // Updated version number for participant names support
        exportDate: new Date().toISOString()
    };

    // Convert the backup object to a JSON string
    const backupJSON = JSON.stringify(backupData, null, 2);

    // Create a Blob with the JSON data
    const blob = new Blob([backupJSON], { type: 'application/json' });

    // Create a URL for the Blob
    const url = URL.createObjectURL(blob);

    // Create a temporary anchor element to trigger the download
    const a = document.createElement('a');
    a.href = url;

    // Generate a filename with the current date
    const date = new Date();
    const dateString = date.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    a.download = `bible_quiz_backup_${dateString}.json`;

    // Append the anchor to the body, click it, and remove it
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // Release the URL object
    URL.revokeObjectURL(url);

    // Show a success message
    alert('Backup exported successfully! The file has been downloaded to your device.');
}

// Import backup of quiz settings, rounds, questions, and teams
function importBackup(event) {
    const file = event.target.files[0];

    if (!file) {
        alert('No file selected.');
        return;
    }

    // Check if the file is a JSON file
    if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
        alert('Please select a valid JSON backup file.');
        return;
    }

    // Create a FileReader to read the file
    const reader = new FileReader();

    reader.onload = function(e) {
        try {
            // Parse the JSON data
            const backupData = JSON.parse(e.target.result);

            // Validate the backup data
            if (!backupData.rounds || !backupData.teams || !backupData.settings) {
                throw new Error('Invalid backup file format. Missing required data.');
            }

            // Confirm before overwriting existing data
            if (confirm('This will replace all your current rounds, questions, teams, and settings. Are you sure you want to continue?')) {
                // Import the data
                rounds = backupData.rounds;
                teams = backupData.teams;
                currentSettings = backupData.settings;

                // Import participant names if available
                if (backupData.participantNames) {
                    participantNames = backupData.participantNames;
                }

                // Import question images if available
                if (backupData.questionImages) {
                    questionImages = backupData.questionImages;
                }

                // Update flattened questions array
                updateFlattenedQuestions();

                // Save the imported data
                saveData();

                // Reset the current quiz state
                resetQuizState();

                // Update the UI
                renderTeams();
                renderRoundsList();
                updateRoundSelect();
                renderQuestionsList();
                renderSettingsTeamsList();
                updateSettingsForm();

                // Apply the saved title to the header
                const titleElement = document.querySelector('header h1');
                if (titleElement && currentSettings.quizTitle) {
                    titleElement.textContent = currentSettings.quizTitle;
                }

                // Apply styles
                applyTitleStyles();
                applyQuestionStyles();
                applyTimerStyles();

                // Show a success message
                alert('Backup imported successfully!');
            }
        } catch (error) {
            console.error('Error importing backup:', error);
            alert('Error importing backup: ' + error.message);
        }

        // Reset the file input
        event.target.value = '';
    };

    reader.onerror = function() {
        alert('Error reading the backup file.');
        // Reset the file input
        event.target.value = '';
    };

    // Read the file as text
    reader.readAsText(file);
}

// Reset the quiz state
function resetQuizState() {
    currentQuizState = {
        currentQuestionIndex: 0,
        currentTeamIndex: 0,
        currentRoundIndex: 0,
        currentRoundQuestionNumber: 0,
        score: teams.map(() => 0),
        timer: null,
        timeLeft: currentSettings.timerDuration,
        questionAnswered: false,
        timerPaused: false,
        teamQuestions: teams.map(() => []),
        availableQuestions: [],
        usedQuestions: [],
        roundsCompleted: false,
        completedRounds: [],
        roundQuestions: []
    };

    quizInProgress = false;
    quizPaused = false;

    // Save the reset state
    saveQuizState();
}

// Render teams in the main view
function renderTeams() {
    teamsContainer.innerHTML = '';

    if (teams.length === 0) {
        teamsContainer.innerHTML = '<p>No teams added yet. Add a team to start the quiz.</p>';
        startQuizBtn.disabled = true;
        return;
    }

    startQuizBtn.disabled = false;

    teams.forEach((team, index) => { // Added index here for use in buttons
        const teamCard = document.createElement('div');
        teamCard.className = 'team-card';
        if (team.backgroundColor) {
            teamCard.style.background = team.backgroundColor;
        }
        teamCard.innerHTML = `
            <div class="team-card-header">
                <h3>${team.name}</h3>
                <div class="team-actions">
                    <button class="edit-team-btn" data-index="${index}"><i class="fas fa-edit"></i></button>
                    <button class="delete-team-btn" data-index="${index}"><i class="fas fa-trash"></i></button>
                </div>
            </div>
            <ul class="participants-list">
                ${team.participants.map(participant => `
                    <li>
                        <img src="${participant.profileImage ? 'profile_images/' + participant.profileImage : 'https://via.placeholder.com/50'}" alt="${participant.name}" class="participant-profile-img">
                        <span class="participant-name">${participant.name}</span>
                    </li>
                `).join('')}
            </ul>
        `;
        teamsContainer.appendChild(teamCard);
    });

    // Add event listeners for edit and delete buttons
    teamsContainer.querySelectorAll('.edit-team-btn').forEach(btn => {
        btn.addEventListener('click', (event) => {
            event.stopPropagation(); // Prevent card click from interfering
            const index = parseInt(btn.dataset.index);
            openEditTeamModal(index);
        });
    });

    teamsContainer.querySelectorAll('.delete-team-btn').forEach(btn => {
        btn.addEventListener('click', (event) => {
            event.stopPropagation(); // Prevent card click from interfering
            const index = parseInt(btn.dataset.index);
            deleteTeam(index);
        });
    });

    // Add event listeners for team card clicks (team introduction)
    teamsContainer.querySelectorAll('.team-card').forEach((card, index) => {
        card.addEventListener('click', (event) => {
            // Don't trigger if clicking on action buttons
            if (event.target.closest('.team-actions')) {
                return;
            }
            openTeamIntroduction(index);
        });
    });
}

// Delete a team
function deleteTeam(index) {
    if (confirm(`Are you sure you want to delete the team "${teams[index].name}"? This action cannot be undone.`)) {
        teams.splice(index, 1);
        saveData();
        renderTeams();
        renderSettingsTeamsList(); // Also update the settings list
    }
}

// Team Introduction Modal Functions
function openTeamIntroduction(teamIndex) {
    const team = teams[teamIndex];
    if (!team) return;

    const modal = document.getElementById('teamIntroductionModal');
    const teamNameEl = document.getElementById('teamIntroName');
    const membersContainer = document.getElementById('teamIntroMembers');

    // Store the currently focused element to restore later
    const previouslyFocusedElement = document.activeElement;
    modal.setAttribute('data-previous-focus', previouslyFocusedElement.id || '');

    // Set team name
    teamNameEl.textContent = team.name;

    // Clear previous members
    membersContainer.innerHTML = '';

    // Create member cards
    team.participants.forEach((participant, index) => {
        const memberCard = document.createElement('div');
        memberCard.className = 'team-member-card';
        memberCard.setAttribute('role', 'listitem');
        memberCard.setAttribute('tabindex', '0');
        memberCard.innerHTML = `
            <div class="member-profile-container">
                <img src="${participant.profileImage ? 'profile_images/' + participant.profileImage : 'https://via.placeholder.com/120'}"
                     alt="Profile picture of ${participant.name}"
                     class="member-profile-img">
            </div>
            <h3 class="member-name">${participant.name}</h3>
        `;
        membersContainer.appendChild(memberCard);
    });

    // Show modal and update accessibility attributes
    modal.classList.add('active');
    modal.setAttribute('aria-hidden', 'false');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling

    // Focus management - focus the close button after a short delay
    setTimeout(() => {
        const closeBtn = document.getElementById('teamIntroCloseBtn');
        if (closeBtn) {
            closeBtn.focus();
        }
    }, 100);

    // Trigger confetti effect
    triggerConfetti();

    // Animate team name entrance (CSS animation will handle this)
    // Reset animation by removing and re-adding class
    teamNameEl.style.animation = 'none';
    teamNameEl.offsetHeight; // Trigger reflow
    teamNameEl.style.animation = null;

    // Animate member cards sequentially
    const memberCards = membersContainer.querySelectorAll('.team-member-card');
    memberCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate-in');

            // Animate member name after card animation
            const memberName = card.querySelector('.member-name');
            setTimeout(() => {
                memberName.classList.add('animate-in');
            }, 400); // Delay for name animation
        }, index * 200); // Stagger card animations
    });
}

function closeTeamIntroduction() {
    const modal = document.getElementById('teamIntroductionModal');

    // Restore focus to the previously focused element
    const previousFocusId = modal.getAttribute('data-previous-focus');
    if (previousFocusId) {
        const previousElement = document.getElementById(previousFocusId);
        if (previousElement) {
            previousElement.focus();
        }
    }

    // Hide modal and update accessibility attributes
    modal.classList.remove('active');
    modal.setAttribute('aria-hidden', 'true');
    document.body.style.overflow = ''; // Restore background scrolling

    // Reset animations for next time
    setTimeout(() => {
        const memberCards = modal.querySelectorAll('.team-member-card');
        const memberNames = modal.querySelectorAll('.member-name');

        memberCards.forEach(card => {
            card.classList.remove('animate-in');
        });

        memberNames.forEach(name => {
            name.classList.remove('animate-in');
        });
    }, 300); // Wait for modal close transition
}

function triggerConfetti() {
    // Check if confetti library is available
    if (typeof confetti === 'undefined') {
        console.warn('Confetti library not loaded');
        return;
    }

    // Play fireworks sound
    fireworksAudio.currentTime = 0;
    fireworksAudio.play().catch(error => {
        console.error('Error playing fireworks audio:', error);
    });

    // Create multiple confetti bursts for a more spectacular effect
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 3000 };

    function randomInRange(min, max) {
        return Math.random() * (max - min) + min;
    }

    const interval = setInterval(function() {
        const timeLeft = animationEnd - Date.now();

        if (timeLeft <= 0) {
            return clearInterval(interval);
        }

        const particleCount = 50 * (timeLeft / duration);

        // Create confetti from multiple points
        confetti(Object.assign({}, defaults, {
            particleCount,
            origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
        }));
        confetti(Object.assign({}, defaults, {
            particleCount,
            origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
        }));
    }, 250);

    // Additional burst from center
    setTimeout(() => {
        confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 },
            zIndex: 3000
        });
    }, 500);
}

// Render teams in the settings view
function renderSettingsTeamsList() {
    settingsTeamsList.innerHTML = '';

    teams.forEach((team, index) => {
        const teamItem = document.createElement('div');
        teamItem.className = 'question-item';
        teamItem.innerHTML = `
            <h3>${team.name}</h3>
            <ul class="question-options">
                ${team.participants.map(participant => `
                    <li>
                        <img src="${participant.profileImage ? 'profile_images/' + participant.profileImage : 'https://via.placeholder.com/30'}" alt="${participant.name}" class="participant-profile-img" style="width: 30px; height: 30px; margin-right: 10px; border: 1px solid #ddd;">
                        ${participant.name}
                    </li>
                `).join('')}
            </ul>
            <div class="question-actions">
                <button class="edit-question-btn" data-index="${index}"><i class="fas fa-edit"></i></button>
                <button class="delete-question-btn" data-index="${index}"><i class="fas fa-trash"></i></button>
            </div>
        `;
        settingsTeamsList.appendChild(teamItem);
    });

    // Add event listeners for edit and delete buttons
    settingsTeamsList.querySelectorAll('.edit-question-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const index = parseInt(btn.dataset.index);
            openEditTeamModal(index);
        });
    });

    settingsTeamsList.querySelectorAll('.delete-question-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const index = parseInt(btn.dataset.index);
            if (confirm(`Are you sure you want to delete the team "${teams[index].name}"?`)) {
                teams.splice(index, 1);
                saveData();
                renderTeams();
                renderSettingsTeamsList();
            }
        });
    });
}

// Render rounds list in settings
function renderRoundsList() {
    roundsList.innerHTML = '';

    if (rounds.length === 0) {
        roundsList.innerHTML = '<p>No rounds added yet. Add a round to start the quiz.</p>';
        return;
    }

    rounds.forEach((round, index) => {
        const roundItem = document.createElement('div');
        roundItem.className = 'round-item';
        // Determine the count and type text based on round type
        let countText = '';
        if (round.type === 'anagrams') {
            countText = `${round.anagrams ? round.anagrams.length : 0} anagrams`;
        } else {
            countText = `${round.questions ? round.questions.length : 0} questions`;
        }

        roundItem.innerHTML = `
            <h3>${round.name} <span class="question-count">(${countText})</span></h3>
            <div class="round-actions">
                <button class="edit-round-btn" data-index="${index}"><i class="fas fa-edit"></i></button>
                <button class="delete-round-btn" data-index="${index}"><i class="fas fa-trash"></i></button>
            </div>
        `;
        roundsList.appendChild(roundItem);
    });

    // Add event listeners for edit and delete buttons
    roundsList.querySelectorAll('.edit-round-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const index = parseInt(btn.dataset.index);
            openEditRoundModal(index);
        });
    });

    roundsList.querySelectorAll('.delete-round-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const index = parseInt(btn.dataset.index);
            if (confirm(`Are you sure you want to delete the round "${rounds[index].name}"? This will also delete all questions in this round.`)) {
                rounds.splice(index, 1);
                saveData();
                renderRoundsList();
                updateRoundSelect();
                renderQuestionsList();
            }
        });
    });
}

// Update the round select dropdown
function updateRoundSelect() {
    questionRoundSelect.innerHTML = '';

    rounds.forEach((round, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = round.name;
        questionRoundSelect.appendChild(option);
    });

    // If no rounds, add a default option
    if (rounds.length === 0) {
        const option = document.createElement('option');
        option.value = -1;
        option.textContent = 'No rounds available';
        option.disabled = true;
        option.selected = true;
        questionRoundSelect.appendChild(option);
    }
}

// Open add round modal
function openAddRoundModal() {
    roundNameInput.value = '';

    // Reset timer settings to defaults
    const timerDurationInput = document.getElementById('roundTimerDuration');
    if (timerDurationInput) {
        timerDurationInput.value = 30;
    }

    const timerSoundSelect = document.getElementById('roundTimerSound');
    if (timerSoundSelect) {
        timerSoundSelect.value = 'clock_timer.mp3';
    }

    roundModalTitle.textContent = 'Add New Round';
    saveRoundBtn.dataset.mode = 'add';
    saveRoundBtn.dataset.index = '';
    addRoundModal.classList.add('active');
}

// Open edit round modal
function openEditRoundModal(index) {
    const round = rounds[index];
    roundNameInput.value = round.name;

    // Set timer duration (use default if not set)
    const timerDurationInput = document.getElementById('roundTimerDuration');
    if (timerDurationInput) {
        timerDurationInput.value = round.timerDuration || 30;
    }

    // Set timer sound (use default if not set)
    const timerSoundSelect = document.getElementById('roundTimerSound');
    if (timerSoundSelect) {
        timerSoundSelect.value = round.timerSound || 'clock_timer.mp3';
    }

    roundModalTitle.textContent = 'Edit Round';
    saveRoundBtn.dataset.mode = 'edit';
    saveRoundBtn.dataset.index = index;
    addRoundModal.classList.add('active');
}

// Save round
function saveRound() {
    const name = roundNameInput.value.trim();
    const type = document.getElementById('roundTypeSelect').value;
    const timerDuration = parseInt(document.getElementById('roundTimerDuration').value);
    const timerSound = document.getElementById('roundTimerSound').value;

    if (!name) {
        alert('Please enter a round name.');
        return;
    }

    if (isNaN(timerDuration) || timerDuration < 5 || timerDuration > 120) {
        alert('Please enter a valid timer duration between 5 and 120 seconds.');
        return;
    }

    const mode = saveRoundBtn.dataset.mode;

    if (mode === 'add') {
        // Add new round
        const newRound = {
            name: name,
            type: type,
            timerDuration: timerDuration,
            timerSound: timerSound
        };

        if (type === 'anagrams') {
            newRound.anagrams = [];
        } else {
            newRound.questions = [];
        }

        rounds.push(newRound);
    } else {
        // Edit existing round
        const index = parseInt(saveRoundBtn.dataset.index);
        rounds[index].name = name;
        rounds[index].timerDuration = timerDuration;
        rounds[index].timerSound = timerSound;
        // Note: We don't change the type of existing rounds to avoid data loss
    }

    saveData();
    renderRoundsList();
    updateRoundSelect();
    addRoundModal.classList.remove('active');
}

// Render questions list in settings
function renderQuestionsList() {
    questionsList.innerHTML = '';

    // Get the selected round index
    const roundIndex = parseInt(questionRoundSelect.value);

    // If no rounds or invalid selection, show message
    if (roundIndex < 0 || roundIndex >= rounds.length) {
        questionsList.innerHTML = '<p>Please add a round first before adding questions.</p>';
        return;
    }

    const round = rounds[roundIndex];

    // Handle different round types
    if (round.type === 'anagrams') {
        // Show anagrams
        if (!round.anagrams || round.anagrams.length === 0) {
            questionsList.innerHTML = '<p>No anagrams added to this round yet. Add an anagram to start the quiz.</p>';
            return;
        }

        round.anagrams.forEach((anagram, index) => {
            const anagramItem = document.createElement('div');
            anagramItem.className = 'question-item';

            // Create hint letters display
            const hintLettersDisplay = Object.keys(anagram.hintLetters).length > 0
                ? Object.entries(anagram.hintLetters).map(([pos, letter]) => `${pos}:${letter}`).join(', ')
                : 'None';

            const anagramContent = `
                <h3>Anagram: ${anagram.word}</h3>
                <div class="anagram-details">
                    <p><strong>Hint Letters:</strong> ${hintLettersDisplay}</p>
                    <p><strong>Random Letters:</strong> ${anagram.randomLetters.join(', ')}</p>
                    <p><strong>Correct Sequence:</strong> ${anagram.correctSequence.join(' → ')}</p>
                </div>
                <div class="question-actions">
                    <button class="edit-anagram-btn" data-index="${index}"><i class="fas fa-edit"></i></button>
                    <button class="delete-anagram-btn" data-index="${index}"><i class="fas fa-trash"></i></button>
                </div>
            `;

            anagramItem.innerHTML = anagramContent;
            questionsList.appendChild(anagramItem);
        });

        // Add event listeners for anagram edit and delete buttons
        questionsList.querySelectorAll('.edit-anagram-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const index = parseInt(btn.dataset.index);
                openEditAnagramModal(index, roundIndex);
            });
        });

        questionsList.querySelectorAll('.delete-anagram-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const index = parseInt(btn.dataset.index);
                if (confirm(`Are you sure you want to delete this anagram?`)) {
                    rounds[roundIndex].anagrams.splice(index, 1);
                    saveData();
                    renderQuestionsList();
                    renderRoundsList();
                }
            });
        });
    } else {
        // Show questions
        if (!round.questions || round.questions.length === 0) {
            questionsList.innerHTML = '<p>No questions added to this round yet. Add a question to start the quiz.</p>';
            return;
        }

        round.questions.forEach((question, index) => {
            const questionItem = document.createElement('div');
            questionItem.className = 'question-item';

            // Create the question content with image if available
            let questionContent = `<h3>${question.text}</h3>`;

            // Add image if it exists
            if (question.image) {
                questionContent += `
                    <div class="question-image-container">
                        <img src="images/${question.image}" alt="Question Image" class="question-image">
                    </div>
                `;
            }

            questionContent += `
                <ul class="question-options">
                    ${question.options.map((option, i) =>
                        `<li ${i === question.correctOption ? 'class="correct"' : ''}>${option} ${i === question.correctOption ? '(Correct)' : ''}</li>`
                    ).join('')}
                </ul>
                <div class="question-actions">
                    <button class="edit-question-btn" data-index="${index}"><i class="fas fa-edit"></i></button>
                    <button class="delete-question-btn" data-index="${index}"><i class="fas fa-trash"></i></button>
                </div>
            `;

            questionItem.innerHTML = questionContent;
            questionsList.appendChild(questionItem);
        });

        // Add event listeners for edit and delete buttons
        questionsList.querySelectorAll('.edit-question-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const index = parseInt(btn.dataset.index);
                openEditQuestionModal(index, roundIndex);
            });
        });

        questionsList.querySelectorAll('.delete-question-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const index = parseInt(btn.dataset.index);
                if (confirm(`Are you sure you want to delete this question?`)) {
                    rounds[roundIndex].questions.splice(index, 1);
                    saveData();
                    renderQuestionsList();
                    updateFlattenedQuestions();
                }
            });
        });
    }
}

// Update settings form with current values
function updateSettingsForm() {
    // Set quiz title
    if (quizTitleInput) {
        quizTitleInput.value = currentSettings.quizTitle || 'Bible Quiz Competition';
    }

    timerDurationInput.value = currentSettings.timerDuration;

    // Set points per correct answer
    const pointsPerCorrectAnswerInput = document.getElementById('pointsPerCorrectAnswer');
    if (pointsPerCorrectAnswerInput) {
        pointsPerCorrectAnswerInput.value = currentSettings.pointsPerCorrectAnswer || 1;
    }

    shuffleQuestionsInput.checked = currentSettings.shuffleQuestions;

    // Set title alignment value
    if (titleAlignmentInput) {
        titleAlignmentInput.value = currentSettings.titleAlignment || 'left';
    }

    // Set title size slider value
    if (titleSizeInput) {
        // Convert from stored value to slider value
        const sizeValue = parseFloat(currentSettings.titleSize) || 2;
        titleSizeInput.value = sizeValue;
        updateSliderValueDisplay();
    }

    // Set question alignment value
    if (questionAlignmentInput) {
        questionAlignmentInput.value = currentSettings.questionAlignment || 'left';
    }

    // Set question size slider value
    if (questionSizeInput) {
        // Convert from stored value to slider value
        const sizeValue = parseFloat(currentSettings.questionSize) || 1.5;
        questionSizeInput.value = sizeValue;
        updateQuestionSliderValueDisplay();
    }

    // Set timer size slider value
    if (timerSizeInput) {
        // Convert from stored value to slider value
        const sizeValue = parseFloat(currentSettings.timerSize) || 1;
        timerSizeInput.value = sizeValue;
        updateTimerSliderValueDisplay();
    }

    // Apply title, question, and timer styles
    applyTitleStyles();
    applyQuestionStyles();
    applyTimerStyles();
}

// Start the quiz by showing round selection
function startQuiz() {
    if (teams.length === 0) {
        alert('Please add at least one team to start the quiz.');
        return;
    }

    if (rounds.length === 0 || rounds.every(round =>
        (round.type === 'anagrams' ? (!round.anagrams || round.anagrams.length === 0) : (!round.questions || round.questions.length === 0))
    )) {
        alert('Please add at least one round with questions or anagrams to start the quiz.');
        return;
    }

    // Initialize quiz state
    currentQuizState.currentQuestionIndex = 0;
    currentQuizState.currentTeamIndex = 0;
    currentQuizState.currentRoundQuestionNumber = 0; // Reset the round question counter
    currentQuizState.score = teams.map(() => 0);
    currentQuizState.questionAnswered = false;
    currentQuizState.timerPaused = false;
    currentQuizState.roundsCompleted = false;
    currentQuizState.completedRounds = []; // Reset completed rounds

    // Initialize tracking of questions for each team
    currentQuizState.teamQuestions = teams.map(() => []);

    // Reset global tracking of used questions
    currentQuizState.usedQuestions = [];

    // Hide teams section and show round selection section
    document.querySelector('.teams-section').classList.add('hidden');
    roundSelectionSection.classList.remove('hidden');

    // Render the available rounds
    renderRoundSelection();
}

// Render the round selection screen
function renderRoundSelection() {
    // Clear the container
    roundsSelectionContainer.innerHTML = '';

    // Add each round as a card
    rounds.forEach((round, index) => {
        const isCompleted = currentQuizState.completedRounds.includes(index);
        const isInProgress = quizInProgress &&
                           currentQuizState.currentRoundIndex === index &&
                           !isCompleted;

        // Determine the current question number for in-progress rounds
        let progressText = '';
        if (isInProgress && currentQuizState.currentRoundQuestionNumber > 0) {
            const totalItems = round.type === 'anagrams' ? (round.anagrams ? round.anagrams.length : 0) : (round.questions ? round.questions.length : 0);
            const itemType = round.type === 'anagrams' ? 'Anagram' : 'Question';
            progressText = `<p class="progress-text">${itemType} ${currentQuizState.currentRoundQuestionNumber} of ${totalItems}</p>`;
        }

        const roundCard = document.createElement('div');
        roundCard.className = `round-card ${isCompleted ? 'completed' : ''} ${isInProgress ? 'in-progress' : ''}`;

        // Determine the count and type text based on round type
        let countText = '';
        if (round.type === 'anagrams') {
            countText = `${round.anagrams ? round.anagrams.length : 0} anagrams`;
        } else {
            countText = `${round.questions ? round.questions.length : 0} questions`;
        }

        roundCard.innerHTML = `
            <h3>${round.name}</h3>
            <div class="round-info">
                <p>${countText}</p>
                ${isCompleted ? '<p class="completed-text">Completed</p>' : ''}
                ${isInProgress ? '<p class="in-progress-text">In Progress</p>' : ''}
                ${progressText}
            </div>
        `;

        // Add click event to start this round
        roundCard.addEventListener('click', () => {
            startRound(index);
        });

        roundsSelectionContainer.appendChild(roundCard);
    });
}

// Start a specific round
function startRound(roundIndex) {
    console.log("Starting round:", roundIndex, "Current round index:", currentQuizState.currentRoundIndex);

    // ALWAYS start fresh when selecting a round (no continuation logic)
    // This ensures clean state for each round selection
    console.log("Starting fresh with round:", roundIndex);

    // Set the current round index
    currentQuizState.currentRoundIndex = roundIndex;

    // Check if this round has already been completed
    const isCompletedRound = currentQuizState.completedRounds.includes(roundIndex);

    if (isCompletedRound) {
        console.log("This round was already completed. Starting it again.");
        // If the round was already completed, we're starting it fresh
        // Remove it from completed rounds so we can play it again
        const completedIndex = currentQuizState.completedRounds.indexOf(roundIndex);
        if (completedIndex !== -1) {
            currentQuizState.completedRounds.splice(completedIndex, 1);
        }
    }

    // Reset all round-specific state
    currentQuizState.questionAnswered = false;
    currentQuizState.currentTeamIndex = 0;
    currentQuizState.currentRoundQuestionNumber = 0;
    currentQuizState.teamQuestions = teams.map(() => []);
    currentQuizState.usedQuestions = [];
    currentQuizState.roundQuestions = [];

    // Reset anagram state
    currentQuizState.anagramState = {
        currentAnagramIndex: 0,
        clickedLetters: [],
        correctSequence: [],
        anagramCompleted: false,
        firstAttempt: true
    };

    // Reset timer state
    const timerSettings = getCurrentRoundTimerSettings();
    currentQuizState.timeLeft = timerSettings.duration;
    currentQuizState.timerPaused = false;
    if (currentQuizState.timer) {
        clearInterval(currentQuizState.timer);
        currentQuizState.timer = null;
    }

    // Load the round's questions/anagrams
    loadRound(roundIndex);

    // Make sure the score array is properly initialized
    if (!currentQuizState.score || currentQuizState.score.length !== teams.length) {
        currentQuizState.score = teams.map(() => 0);
    } else {
        // Ensure all scores are valid numbers
        for (let i = 0; i < currentQuizState.score.length; i++) {
            if (isNaN(currentQuizState.score[i]) || currentQuizState.score[i] === undefined) {
                currentQuizState.score[i] = 0;
            }
        }
    }

    // Reset Next Question button to its original state
    const nextBtn = document.getElementById('nextQuestionBtn');
    nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
    nextBtn.classList.remove('pause-btn');
    nextBtn.disabled = true;
    nextBtn.onclick = nextQuestion;

    // Hide all sections and show quiz section
    menuSection.classList.add('hidden');
    teamsSection.classList.add('hidden');
    roundSelectionSection.classList.add('hidden');
    resultsSection.classList.add('hidden');
    quizSection.classList.remove('hidden');

    // Play background music when starting a round
    playBackgroundMusic();

    // Set quiz in progress
    quizInProgress = true;
    quizPaused = false;

    // Save the current state
    saveQuizState();

    // Update available questions for the current team
    updateAvailableQuestions();

    // Always load the first question for a fresh start
    loadQuestion();
}

// Load a specific round's questions or anagrams
function loadRound(roundIndex) {
    if (roundIndex >= rounds.length) {
        currentQuizState.roundsCompleted = true;
        return;
    }

    const round = rounds[roundIndex];
    console.log("Loading round:", round.name, "type:", round.type);

    // Set the current round type
    currentQuizState.currentRoundType = round.type || "questions";

    if (round.type === "anagrams") {
        // Handle anagram round
        console.log("Loading anagram round with", round.anagrams.length, "anagrams");

        // Store anagrams for this round (no shuffling for anagrams to maintain hint consistency)
        currentQuizState.roundQuestions = round.anagrams;

        // Update total count for anagrams
        if (totalQuestionsEl) {
            totalQuestionsEl.textContent = round.anagrams.length;
        }
    } else {
        // Handle question round
        console.log("Loading question round with", round.questions.length, "questions");

        // Get the questions for this round
        let roundQuestions = [...round.questions];

        // Shuffle questions if enabled
        if (currentSettings.shuffleQuestions) {
            roundQuestions = shuffleArray(roundQuestions);
        }

        // Store the questions for this round
        currentQuizState.roundQuestions = roundQuestions;

        // Update total questions count for this round
        if (totalQuestionsEl) {
            totalQuestionsEl.textContent = roundQuestions.length;
        }
    }

    // When starting a new round, we need to reset the used questions for this round
    // but keep track of which rounds have been completed
    if (!currentQuizState.completedRounds.includes(roundIndex)) {
        // If we're starting a fresh round, reset the used questions
        // We'll only track used questions within the current round
        currentQuizState.usedQuestions = [];
        console.log("Starting fresh round, reset used questions");
    } else {
        console.log("Round was previously completed, keeping used questions");
    }

    // Initialize available questions/anagrams for this round
    currentQuizState.availableQuestions = [];
    for (let i = 0; i < currentQuizState.roundQuestions.length; i++) {
        currentQuizState.availableQuestions.push(i);
    }

    console.log("Available items:", currentQuizState.availableQuestions.length);

    // Reset the round question counter when a new round is loaded
    currentQuizState.currentRoundQuestionNumber = 0;

    // Update the round name display
    updateRoundDisplay(round.name);

    // Save the updated state
    saveQuizState();
}

// Update the round name display
function updateRoundDisplay(roundName) {
    // Update the round name if the element exists
    if (currentRoundEl) {
        currentRoundEl.textContent = roundName;
    }
}

// Update the team score display
function updateTeamScoreDisplay() {
    // Update the team score if the element exists
    if (currentTeamScoreEl && currentQuizState.currentTeamIndex >= 0 && currentQuizState.currentTeamIndex < currentQuizState.score.length) {
        const teamScore = currentQuizState.score[currentQuizState.currentTeamIndex];
        currentTeamScoreEl.textContent = teamScore;
    }
}

// Load current question or anagram
function loadQuestion() {
    // Check if we need to move to the next round
    if (currentQuizState.availableQuestions.length === 0) {
        // End the current round instead of automatically moving to the next one
        endQuiz();
        return;
    }

    // Get a question/anagram that hasn't been used in the quiz yet
    const itemIndex = getNextQuestionForTeam();
    if (itemIndex === -1) {
        // No more items available
        endQuiz();
        return;
    }

    // Play background music when a new item is loaded
    playBackgroundMusic();

    // Increment the round question counter when a new item is loaded
    currentQuizState.currentRoundQuestionNumber++;

    const team = teams[currentQuizState.currentTeamIndex];

    // Track that this item has been shown to this team
    currentQuizState.teamQuestions[currentQuizState.currentTeamIndex].push(itemIndex);

    // Mark this item as used globally
    currentQuizState.usedQuestions.push(itemIndex);

    // Save the state after updating used questions
    saveQuizState();

    // Handle different round types
    if (currentQuizState.currentRoundType === "anagrams") {
        // Load anagram
        loadAnagram(itemIndex, team);
    } else {
        // Load question
        loadQuestionContent(itemIndex, team);
    }
}

// Load anagram content
function loadAnagram(anagramIndex, team) {
    // Update UI for anagram
    currentQuestionEl.textContent = currentQuizState.currentRoundQuestionNumber;
    currentTeamEl.textContent = team.name.toUpperCase();

    // Update round display
    if (currentRoundEl) {
        currentRoundEl.textContent = rounds[currentQuizState.currentRoundIndex].name;
    }

    // Update team score display
    updateTeamScoreDisplay();

    // Apply timer styles
    applyTimerStyles();

    // Reset state
    currentQuizState.questionAnswered = false;
    nextQuestionBtn.disabled = true;

    // Reset timer display
    clearInterval(currentQuizState.timer);
    const timerSettings = getCurrentRoundTimerSettings();
    currentQuizState.timeLeft = timerSettings.duration;
    timerEl.textContent = currentQuizState.timeLeft;
    timerEl.parentElement.style.setProperty('--progress', '100%');

    // Hide anagram container and show question container with loading message
    document.getElementById('anagramContainer').classList.add('hidden');
    document.getElementById('questionContainer').classList.remove('hidden');

    // Show loading message similar to regular questions
    questionTextEl.textContent = "Loading Question...";

    // Hide options container for anagrams
    document.getElementById('optionsContainer').style.display = 'none';

    // Update counter display to show anagram counter
    document.getElementById('questionCounter').classList.add('hidden');
    document.getElementById('anagramCounter').classList.remove('hidden');
    document.getElementById('currentAnagram').textContent = anagramIndex + 1;
    const round = rounds[currentQuizState.currentRoundIndex];
    document.getElementById('totalAnagrams').textContent = round.anagrams.length;

    // Show start timer button
    document.getElementById('startTimerBtn').style.display = 'inline-block';

    // Reset the Next Question button
    const nextBtn = document.getElementById('nextQuestionBtn');
    nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
    nextBtn.classList.remove('pause-btn');
    nextBtn.disabled = true;
    nextBtn.style.display = 'none';

    // Remove any existing click event listeners by cloning the button
    nextBtn.replaceWith(nextBtn.cloneNode(true));

    // Get the fresh button reference after cloning
    const freshNextBtn = document.getElementById('nextQuestionBtn');

    // Add the next question functionality
    freshNextBtn.addEventListener('click', nextQuestion);

    // Set up start timer button for anagram
    const startTimerButton = document.getElementById('startTimerBtn');
    const newStartTimerBtn = startTimerButton.cloneNode(true);
    startTimerButton.parentNode.replaceChild(newStartTimerBtn, startTimerButton);

    // Add the event listener for anagram start
    newStartTimerBtn.addEventListener('click', function() {
        startAnagramTimer(anagramIndex);
    });
}

// Load question content (original question logic)
function loadQuestionContent(questionIndex, team) {
    const question = currentQuizState.roundQuestions[questionIndex];

    // Update UI
    // Update the current question number (global round question number)
    currentQuestionEl.textContent = currentQuizState.currentRoundQuestionNumber;
    currentTeamEl.textContent = team.name.toUpperCase(); // Make team name uppercase
    // Display question text
    questionTextEl.textContent = question.text;
    
    // Display question image if available
    const existingImage = document.querySelector('.question-image');
    const existingImageContainer = document.querySelector('.question-image-container');
    const existingImageButton = document.querySelector('.image-preview-button');

    if (existingImage) {
        existingImage.remove();
    }
    if (existingImageContainer) {
        existingImageContainer.remove();
    }
    if (existingImageButton) {
        existingImageButton.remove();
    }

    if (question.image) {
        // Create image container
        const imageContainer = document.createElement('div');
        imageContainer.className = 'question-image-container';

        // Create image element
        const imageElement = document.createElement('img');
        imageElement.src = `images/${question.image}`;
        imageElement.alt = 'Question Image';
        imageElement.className = 'question-image';

        // Add click event to open modal
        imageElement.addEventListener('click', () => openImagePreviewModal(question.image));

        // Create preview button
        const previewButton = document.createElement('button');
        previewButton.className = 'image-preview-button';
        previewButton.innerHTML = '<i class="fas fa-expand"></i> View Image';
        previewButton.addEventListener('click', () => openImagePreviewModal(question.image));

        // Add image to container
        imageContainer.appendChild(imageElement);

        // Add preview button to container (below the image)
        //const buttonContainer = document.createElement('div');
        //buttonContainer.style.textAlign = 'center';
       // buttonContainer.style.marginTop = '10px';
       // buttonContainer.appendChild(previewButton);
       // imageContainer.appendChild(buttonContainer);

        // Insert the container after the question text
        questionTextEl.parentNode.insertBefore(imageContainer, questionTextEl.nextSibling);

        
        // Insert the preview button after the question text (inline with question)
        const buttonSpan = document.createElement('span');
        buttonSpan.appendChild(previewButton);
        questionTextEl.appendChild(buttonSpan);

    }

    // Update the total questions count for the current round
    totalQuestionsEl.textContent = currentQuizState.roundQuestions.length;

    // Update round display
    if (currentRoundEl) {
        currentRoundEl.textContent = rounds[currentQuizState.currentRoundIndex].name;
    }

    // Update team score display
    updateTeamScoreDisplay();

    // Apply question and timer styles
    applyQuestionStyles();
    applyTimerStyles();

    // Clear options container
    optionsContainerEl.innerHTML = '';

    // Remove any feedback messages
    const feedbackMessages = document.querySelectorAll('.point-message, .no-point-message, .answer-message, .timer-message');
    feedbackMessages.forEach(message => message.remove());

    // Reset state
    currentQuizState.questionAnswered = false;
    nextQuestionBtn.disabled = true;

    // Reset timer display
    clearInterval(currentQuizState.timer);
    const timerSettings = getCurrentRoundTimerSettings();
    currentQuizState.timeLeft = timerSettings.duration;
    timerEl.textContent = currentQuizState.timeLeft;
    timerEl.parentElement.style.setProperty('--progress', '100%');

    // Show start timer button and hide next question button
    document.getElementById('startTimerBtn').style.display = 'inline-block';

    // Reset the Next Question button to its original state
    const nextBtn = document.getElementById('nextQuestionBtn');
    nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
    nextBtn.classList.remove('pause-btn');
    nextBtn.disabled = true;
    nextBtn.style.display = 'none';

    // Remove any existing click event listeners by cloning the button
    nextBtn.replaceWith(nextBtn.cloneNode(true));

    // Get the fresh button reference after cloning
    const freshNextBtn = document.getElementById('nextQuestionBtn');

    // Add the next question functionality
    freshNextBtn.addEventListener('click', nextQuestion);

    // Remove reveal answer button if it exists
    const revealBtn = document.getElementById('revealAnswerBtn');
    if (revealBtn) {
        revealBtn.remove();
    }

    // Make sure the start timer button has an event listener
    // First remove any existing listeners to avoid duplicates
    const startTimerButton = document.getElementById('startTimerBtn');
    const newStartTimerBtn = startTimerButton.cloneNode(true);
    startTimerButton.parentNode.replaceChild(newStartTimerBtn, startTimerButton);

    // Add the event listener to the new button
    newStartTimerBtn.addEventListener('click', function() {
        showOptionsAndStartTimer();
    });
}

// Show options and start timer when the start timer button is clicked
function showOptionsAndStartTimer() {
    // Make sure we have valid team questions
    if (!currentQuizState.teamQuestions ||
        !currentQuizState.teamQuestions[currentQuizState.currentTeamIndex] ||
        currentQuizState.teamQuestions[currentQuizState.currentTeamIndex].length === 0) {
        console.error("No team questions available");

        // Try to recover by loading a new question
        try {
            // Get a question that hasn't been used in the quiz yet
            const questionIndex = getNextQuestionForTeam();
            if (questionIndex === -1) {
                // No more questions available
                alert("No questions available for this team. Returning to menu.");
                showMenu();
                return;
            }

            // Initialize team questions array if needed
            if (!currentQuizState.teamQuestions) {
                currentQuizState.teamQuestions = teams.map(() => []);
            }

            // Track that this question has been shown to this team
            currentQuizState.teamQuestions[currentQuizState.currentTeamIndex].push(questionIndex);

            // Mark this question as used globally
            currentQuizState.usedQuestions.push(questionIndex);

            // Save the state after updating
            saveQuizState();
        } catch (error) {
            console.error("Error recovering from missing team questions:", error);
            alert("There was an error loading the question. Returning to menu.");
            showMenu();
            return;
        }
    }

    // Get the current question index from the team's question list
    const teamQuestions = currentQuizState.teamQuestions[currentQuizState.currentTeamIndex];
    const currentQuestionIndex = teamQuestions[teamQuestions.length - 1];

    // Make sure we have a valid question
    if (!currentQuizState.roundQuestions ||
        !currentQuizState.roundQuestions[currentQuestionIndex]) {
        console.error("Invalid question index:", currentQuestionIndex);

        // Try to recover by reloading the current round
        if (rounds.length > 0 && currentQuizState.currentRoundIndex < rounds.length) {
            loadRound(currentQuizState.currentRoundIndex);
            updateAvailableQuestions();
            loadQuestion();
        } else {
            alert("There was an error loading the question. Returning to menu.");
            showMenu();
        }
        return;
    }

    const question = currentQuizState.roundQuestions[currentQuestionIndex];

    // Clear any existing options
    optionsContainerEl.innerHTML = '';

    // Add options
    question.options.forEach((option, index) => {
        const optionEl = document.createElement('div');
        optionEl.className = 'option';
        optionEl.textContent = option;
        optionEl.dataset.index = index;

        // Make sure the click event is properly attached
        optionEl.addEventListener('click', function() {
            selectOption(index);
        });

        optionsContainerEl.appendChild(optionEl);
    });

    // Hide start timer button
    document.getElementById('startTimerBtn').style.display = 'none';

    // Convert next question button to pause button
    const nextBtn = document.getElementById('nextQuestionBtn');
    nextBtn.innerHTML = '<i class="fas fa-pause"></i> Pause Timer';
    nextBtn.classList.add('pause-btn');
    nextBtn.classList.remove('primary');
    nextBtn.classList.add('skyblue');
    nextBtn.disabled = false;
    nextBtn.style.display = 'inline-block';

    // Set button state in quiz state
    currentQuizState.timerPaused = false;

    // Remove any existing click event listeners
    nextBtn.replaceWith(nextBtn.cloneNode(true));

    // Get the fresh button reference after cloning
    const freshNextBtn = document.getElementById('nextQuestionBtn');

    // Add pause/resume functionality to the button
    freshNextBtn.addEventListener('click', togglePauseTimer);

    // Only initialize timer if it's the first time starting (not resuming)
    // This is the first time showing options, so we should reset the timer
    const timerSettings = getCurrentRoundTimerSettings();
    currentQuizState.timeLeft = timerSettings.duration;
    timerEl.textContent = currentQuizState.timeLeft;

    // Start timer with progress bar
    startTimer();

    // Save the state after showing options
    saveQuizState();
}

// Start the timer
function startTimer() {
    // Clear any existing timer
    clearInterval(currentQuizState.timer);

    // Get current round timer settings
    const timerSettings = getCurrentRoundTimerSettings();

    // Update timer audio to use round-specific sound
    updateTimerAudio();

    // Update the display with current time
    timerEl.textContent = currentQuizState.timeLeft;
    const progress = (currentQuizState.timeLeft / timerSettings.duration) * 100;
    timerEl.parentElement.style.setProperty('--progress', `${progress}%`);

    // Log the current time (for debugging)
    console.log('Starting/resuming timer at:', currentQuizState.timeLeft, 'with sound:', timerSettings.sound);

    // Only play the timer audio if it's not already playing
    if (timerAudio.paused) {
        // If we're resuming from a pause, don't reset the audio position
        // If it's a new timer start, reset to beginning
        if (timerAudio.currentTime === 0 || timerAudio.ended) {
            timerAudio.currentTime = 0;
        }

        timerAudio.play().catch(error => {
            console.error('Error playing timer audio:', error);
        });
    }

    // Start countdown
    currentQuizState.timer = setInterval(() => {
        if (currentQuizState.timeLeft <= 0) {
            clearInterval(currentQuizState.timer);
            // Don't stop the audio - let it play to completion
            return;
        }
        currentQuizState.timeLeft--;
        timerEl.textContent = currentQuizState.timeLeft;

        // Update progress
        const progress = (currentQuizState.timeLeft / timerSettings.duration) * 100;
        timerEl.parentElement.style.setProperty('--progress', `${progress}%`);

        if (currentQuizState.timeLeft <= 0) {
            // Time's up
            clearInterval(currentQuizState.timer);
            // Don't stop the audio - let it play to completion
            if (!currentQuizState.questionAnswered) {
                timeUp();
            }
        }
    }, 1000);
}

// Toggle pause/resume timer
function togglePauseTimer(event) {
    // Prevent any default behavior
    event.preventDefault();
    event.stopPropagation();

    const pauseBtn = document.getElementById('nextQuestionBtn');

    if (!currentQuizState.timerPaused) {
        // Pause the timer
        clearInterval(currentQuizState.timer);
        currentQuizState.timerPaused = true;
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> Resume Timer';

        // Pause the timer audio (don't reset it)
        timerAudio.pause();

        // Store the current time left (for debugging)
        console.log('Paused at:', currentQuizState.timeLeft);

        // Add a visual indication that the timer is paused
        timerEl.parentElement.classList.add('paused');
    } else {
        // Resume the timer
        currentQuizState.timerPaused = false;
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> Pause Timer';

        // Log the time we're resuming from (for debugging)
        console.log('Resuming from:', currentQuizState.timeLeft);

        // Remove the paused visual indication
        timerEl.parentElement.classList.remove('paused');

        // Restart the timer without resetting the audio
        // We'll just resume the audio if it's not ended
        if (timerAudio.paused && !timerAudio.ended) {
            timerAudio.play().catch(error => {
                console.error('Error resuming timer audio:', error);
            });
        }

        // Restart the timer
        startTimer();
    }

    // Return false to prevent any other handlers from executing
    return false;
}

// Start anagram timer and display anagram
function startAnagramTimer(anagramIndex) {
    // Display the anagram
    displayAnagram(anagramIndex);

    // Hide start timer button
    document.getElementById('startTimerBtn').style.display = 'none';

    // Convert next question button to pause button
    const nextBtn = document.getElementById('nextQuestionBtn');
    nextBtn.innerHTML = '<i class="fas fa-pause"></i> Pause Timer';
    nextBtn.classList.add('pause-btn');
    nextBtn.classList.remove('primary');
    nextBtn.classList.add('skyblue');
    nextBtn.disabled = false;
    nextBtn.style.display = 'inline-block';

    // Set button state in quiz state
    currentQuizState.timerPaused = false;

    // Remove any existing click event listeners
    nextBtn.replaceWith(nextBtn.cloneNode(true));

    // Get the fresh button reference after cloning
    const freshNextBtn = document.getElementById('nextQuestionBtn');

    // Add pause/resume functionality to the button
    freshNextBtn.addEventListener('click', togglePauseTimer);

    // Initialize timer
    const timerSettings = getCurrentRoundTimerSettings();
    currentQuizState.timeLeft = timerSettings.duration;
    timerEl.textContent = currentQuizState.timeLeft;

    // Start timer with progress bar
    startTimer();

    // Save the state after showing anagram
    saveQuizState();
}

// Handle time up
function timeUp() {
    // Mark as time up, but not fully answered yet
    clearInterval(currentQuizState.timer);

    // Don't stop the timer audio - let it play to completion
    // The timer audio will continue playing until it finishes naturally

    // Remove any existing feedback messages
    const existingMessages = document.querySelectorAll('.point-message, .no-point-message, .answer-message, .timer-message');
    existingMessages.forEach(msg => msg.remove());

    // Add a simple message indicating time is up
    const timerMessage = document.createElement('div');
    timerMessage.className = 'timer-message';
    timerMessage.innerHTML = '<i class="fas fa-clock"></i> Time\'s Up!';
    timerMessage.style.color = '#dc3545';
    timerMessage.style.fontWeight = 'bold';
    timerMessage.style.fontSize = '1.2rem';
    timerMessage.style.textAlign = 'center';
    timerMessage.style.margin = '15px 0';

    // Insert the message before the options container
    optionsContainerEl.parentNode.insertBefore(timerMessage, optionsContainerEl);

    // Check if we already have a reveal button
    let revealBtn = document.getElementById('revealAnswerBtn');

    // If not, create one
    if (!revealBtn) {
        revealBtn = document.createElement('button');
        revealBtn.id = 'revealAnswerBtn';
        revealBtn.className = 'btn skyblue';
        revealBtn.innerHTML = '<i class="fas fa-eye"></i> Reveal Answer';
        revealBtn.addEventListener('click', revealAnswer);

        // Add it to the quiz controls
        const quizControls = document.querySelector('.quiz-controls');
        const nextBtn = document.getElementById('nextQuestionBtn');
        quizControls.insertBefore(revealBtn, nextBtn);
    }

    // Convert pause button back to next question button
    const nextBtn = document.getElementById('nextQuestionBtn');
    nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
    nextBtn.classList.remove('pause-btn');
    nextBtn.disabled = false;
    nextBtn.style.display = 'inline-block';

    // Remove any existing click event listeners by cloning the button
    nextBtn.replaceWith(nextBtn.cloneNode(true));

    // Get the fresh button reference after cloning
    const freshNextBtn = document.getElementById('nextQuestionBtn');

    // Add the next question functionality
    freshNextBtn.addEventListener('click', nextQuestion);

    // Ensure start timer button is hidden
    startTimerBtn.style.display = 'none';

    // Remove the paused visual indication if it exists
    timerEl.parentElement.classList.remove('paused');
}

// Handle option selection
function selectOption(index) {
    console.log("Option selected:", index);

    if (currentQuizState.questionAnswered) {
        console.log("Question already answered, ignoring selection");
        return;
    }

    // Get the current question index from the team's question list
    const teamQuestions = currentQuizState.teamQuestions[currentQuizState.currentTeamIndex];
    if (!teamQuestions || teamQuestions.length === 0) {
        console.error("No team questions available");
        return;
    }

    const currentQuestionIndex = teamQuestions[teamQuestions.length - 1];
    if (currentQuestionIndex === undefined || !currentQuizState.roundQuestions[currentQuestionIndex]) {
        console.error("Invalid question index:", currentQuestionIndex);
        return;
    }

    const question = currentQuizState.roundQuestions[currentQuestionIndex];
    const options = optionsContainerEl.querySelectorAll('.option');

    if (!options || options.length === 0 || index >= options.length) {
        console.error("Invalid options or index:", options?.length, index);
        return;
    }

    // Track if this is the first attempt
    const isFirstAttempt = !Array.from(options).some(option =>
        option.classList.contains('selected') || option.classList.contains('incorrect'));

    // Show selected option
    options[index].classList.add('selected');

    // Check if correct
    if (index === question.correctOption) {
        // Correct answer selected
        options[index].classList.add('correct');

        // Mark question as answered
        currentQuizState.questionAnswered = true;
        clearInterval(currentQuizState.timer);

        // Stop the timer audio when a correct answer is selected
        if (!timerAudio.paused) {
            timerAudio.pause();
            timerAudio.currentTime = 0;
        }

        // Play the correct sound
        correctAudio.currentTime = 0;
        correctAudio.play().catch(error => {
            console.error('Error playing correct audio:', error);
        });

        // Add points to current team ONLY if this is the first attempt
        if (isFirstAttempt) {
            // Add points based on settings
            const pointsToAdd = currentSettings.pointsPerCorrectAnswer || 1;
            currentQuizState.score[currentQuizState.currentTeamIndex] += pointsToAdd;

            // Update the team score display
            updateTeamScoreDisplay();

            // Save the updated score
            saveQuizState();

            // Show celebration animation for first attempt correct answers
            showCelebrationAnimation(currentQuizState.currentTeamIndex, pointsToAdd);
        } else {
            // Add a simple correct message for subsequent attempts
            const correctMessage = document.createElement('div');
            correctMessage.className = 'point-message';
            correctMessage.innerHTML = '<i class="fas fa-check-circle"></i> Correct Answer!';
            correctMessage.style.color = '#28a745';
            correctMessage.style.fontWeight = 'bold';
            correctMessage.style.fontSize = '1.2rem';
            correctMessage.style.textAlign = 'center';
            correctMessage.style.margin = '15px 0';

            // Insert the message before the options container
            optionsContainerEl.parentNode.insertBefore(correctMessage, optionsContainerEl);

            // Save the state
            saveQuizState();
        }

        // Convert pause button back to next question button
        const nextBtn = document.getElementById('nextQuestionBtn');
        nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
        nextBtn.classList.remove('pause-btn');
        nextBtn.disabled = false;
        nextBtn.style.display = 'inline-block';

        // Remove any existing click event listeners by cloning the button
        nextBtn.replaceWith(nextBtn.cloneNode(true));

        // Get the fresh button reference after cloning
        const freshNextBtn = document.getElementById('nextQuestionBtn');

        // Add the next question functionality
        freshNextBtn.addEventListener('click', nextQuestion);

        // Ensure start timer button is hidden
        startTimerBtn.style.display = 'none';

        // Remove the paused visual indication if it exists
        timerEl.parentElement.classList.remove('paused');

        // Hide reveal answer button if it exists
        const revealBtn = document.getElementById('revealAnswerBtn');
        if (revealBtn) {
            revealBtn.remove();
        }
    } else {
        // Incorrect answer selected
        options[index].classList.add('incorrect');

        // Disable this option but keep others active
        options[index].style.pointerEvents = 'none';

        // Play the wrong sound
        wrongAudio.currentTime = 0;
        wrongAudio.play().catch(error => {
            console.error('Error playing wrong audio:', error);
        });

        // Add a visual indicator that the answer is incorrect
        const incorrectMessage = document.createElement('div');
        incorrectMessage.className = 'no-point-message';
        incorrectMessage.innerHTML = '<i class="fas fa-times-circle"></i> Incorrect Answer';
        incorrectMessage.style.color = '#dc3545';
        incorrectMessage.style.fontWeight = 'bold';
        incorrectMessage.style.fontSize = '1.2rem';
        incorrectMessage.style.textAlign = 'center';
        incorrectMessage.style.margin = '15px 0';

        // Remove any existing incorrect messages
        const existingMessages = document.querySelectorAll('.no-point-message');
        existingMessages.forEach(msg => msg.remove());

        // Insert the message before the options container
        optionsContainerEl.parentNode.insertBefore(incorrectMessage, optionsContainerEl);

        // Check if we already have a reveal button
        let revealBtn = document.getElementById('revealAnswerBtn');

        // If not, create one
        if (!revealBtn) {
            revealBtn = document.createElement('button');
            revealBtn.id = 'revealAnswerBtn';
            revealBtn.className = 'btn skyblue';
            revealBtn.innerHTML = '<i class="fas fa-eye"></i> Reveal Answer';
            revealBtn.addEventListener('click', revealAnswer);

            // Add it to the quiz controls
            const quizControls = document.querySelector('.quiz-controls');
            quizControls.insertBefore(revealBtn, nextQuestionBtn);
        }

        // Don't mark the question as fully answered yet
        // Don't show the correct answer
        // Don't enable the next button

        // Allow another attempt by keeping the timer running
        // We don't set currentQuizState.questionAnswered = true here
    }
}

// Show celebration animation for correct answers
function showCelebrationAnimation(teamIndex, pointsAwarded = 1) {
    const team = teams[teamIndex];

    // We don't need to get the question details anymore since we're not showing the answer

    // Make sure timer audio is stopped when showing celebration
    if (!timerAudio.paused) {
        timerAudio.pause();
        timerAudio.currentTime = 0;
    }

    // Play the correct sound (in case it wasn't played earlier)
    if (correctAudio.paused) {
        correctAudio.currentTime = 0;
        correctAudio.play().catch(error => {
            console.error('Error playing correct audio in celebration:', error);
        });
    }

    // Play fireworks sound for confetti animation
    fireworksAudio.currentTime = 0;
    fireworksAudio.play().catch(error => {
        console.error('Error playing fireworks audio:', error);
    });

    // Create the celebration overlay
    const overlay = document.createElement('div');
    overlay.className = 'celebration-overlay';

    // Create the celebration popup
    const popup = document.createElement('div');
    popup.className = 'celebration-popup';

    // Add content to the popup
    popup.innerHTML = `
        <h2>Congratulations!</h2>
        <p>${team.name} got the</p>
        <p style="color: #28a745; font-weight: bold; margin-top: 5px; font-size: 1.5em;">Correct Answer!</p>
        <span class="points-awarded">+${pointsAwarded} ${pointsAwarded === 1 ? 'Point' : 'Points'} Awarded</span>
        <div style="display: flex; justify-content: center; margin-top: 20px;">
            <button id="continueBtn" class="btn skyblue">CONTINUE</button>
        </div>
    `;

    // Add confetti elements
    for (let i = 0; i < 100; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'celebration-confetti';

        // Random position, color, and delay
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.animationDelay = Math.random() * 3 + 's';

        // Random colors - more vibrant colors
        const colors = ['#ffcc00', '#ff6b6b', '#4ecdc4', '#45b7d8', '#8a2be2', '#ff8c00', '#28a745', '#20c997', '#fd7e14', '#e83e8c'];
        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];

        // Random size
        const size = Math.random() * 12 + 5;
        confetti.style.width = size + 'px';
        confetti.style.height = size + 'px';

        // Random rotation
        confetti.style.transform = `rotate(${Math.random() * 360}deg)`;

        overlay.appendChild(confetti);
    }

    // Add star elements
    for (let i = 0; i < 30; i++) {
        const star = document.createElement('div');
        star.className = 'celebration-star';

        // Random position and delay
        star.style.left = Math.random() * 100 + '%';
        star.style.animationDelay = Math.random() * 2 + 's';

        // Random colors
        const colors = ['#ffcc00', '#ffd700', '#ffdf00', '#f0e68c', '#ffff00'];
        const color = colors[Math.floor(Math.random() * colors.length)];
        star.style.borderBottomColor = color;

        // Set the same color for before and after pseudo-elements
        const styleSheet = document.createElement('style');
        const randomId = 'star-' + Math.floor(Math.random() * 1000000);
        star.id = randomId;
        styleSheet.textContent = `
            #${randomId}:before, #${randomId}:after {
                border-bottom-color: ${color};
            }
        `;
        document.head.appendChild(styleSheet);

        overlay.appendChild(star);
    }

    // Add firework elements
    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            const firework = document.createElement('div');
            firework.className = 'celebration-firework';

            // Random position
            firework.style.left = 20 + Math.random() * 60 + '%';
            firework.style.top = 20 + Math.random() * 60 + '%';

            // Random colors
            const colors = ['#ffcc00', '#ff6b6b', '#4ecdc4', '#45b7d8', '#8a2be2', '#ff8c00', '#28a745'];
            firework.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            firework.style.boxShadow = `0 0 10px 2px ${colors[Math.floor(Math.random() * colors.length)]}`;

            // Random direction
            const angle = Math.random() * Math.PI * 2;
            const distance = 50 + Math.random() * 100;
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;
            firework.style.setProperty('--x', `${x}px`);
            firework.style.setProperty('--y', `${y}px`);

            overlay.appendChild(firework);

            // Remove after animation completes
            setTimeout(() => {
                firework.remove();
            }, 1000);
        }, i * 100); // Stagger the fireworks
    }

    // Add the popup to the overlay
    overlay.appendChild(popup);

    // Add the overlay to the body
    document.body.appendChild(overlay);

    // Add event listener to the continue button
    document.getElementById('continueBtn').addEventListener('click', function() {
        // Remove the overlay
        overlay.remove();

        // Handle different round types
        if (currentQuizState.currentRoundType === "anagrams") {
            // For anagrams, enable the next question button
            const nextBtn = document.getElementById('nextQuestionBtn');
            nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
            nextBtn.classList.remove('pause-btn');
            nextBtn.disabled = false;
            nextBtn.style.display = 'inline-block';

            // Remove any existing click event listeners
            nextBtn.replaceWith(nextBtn.cloneNode(true));

            // Get the fresh button reference after cloning
            const freshNextBtn = document.getElementById('nextQuestionBtn');

            // Add the next question functionality
            freshNextBtn.addEventListener('click', nextQuestion);
        } else {
            // For regular questions, add the correct message
            // Remove any existing feedback messages first
            const existingMessages = document.querySelectorAll('.point-message, .no-point-message, .answer-message, .timer-message');
            existingMessages.forEach(msg => msg.remove());

            // Add a simple correct message to the quiz
            const correctMessage = document.createElement('div');
            correctMessage.className = 'point-message';
            correctMessage.innerHTML = '<i class="fas fa-check-circle"></i> Correct Answer!';
            correctMessage.style.color = '#28a745';
            correctMessage.style.fontWeight = 'bold';
            correctMessage.style.fontSize = '1.2rem';
            correctMessage.style.textAlign = 'center';
            correctMessage.style.margin = '15px 0';

            // Insert the message before the options container
            optionsContainerEl.parentNode.insertBefore(correctMessage, optionsContainerEl);
        }
    });
}

// Reveal the correct answer
function revealAnswer() {
    // Check if we're in an anagram round - if so, this function shouldn't be called
    if (currentQuizState.currentRoundType === 'anagrams') {
        console.warn('revealAnswer called during anagram round - this should not happen');
        return;
    }

    // Mark the question as fully answered now
    currentQuizState.questionAnswered = true;
    clearInterval(currentQuizState.timer);

    // Don't stop the timer audio - let it play to completion
    // The timer audio will continue playing until it finishes naturally

    // Check if we have valid team questions structure
    if (!currentQuizState.teamQuestions ||
        !currentQuizState.teamQuestions[currentQuizState.currentTeamIndex] ||
        currentQuizState.teamQuestions[currentQuizState.currentTeamIndex].length === 0) {
        console.error('Invalid team questions structure in revealAnswer');
        return;
    }

    // Get the current question index from the team's question list
    const teamQuestions = currentQuizState.teamQuestions[currentQuizState.currentTeamIndex];
    const currentQuestionIndex = teamQuestions[teamQuestions.length - 1];
    const question = currentQuizState.roundQuestions[currentQuestionIndex];

    if (!question) {
        console.error('No question found at index:', currentQuestionIndex);
        return;
    }

    const options = optionsContainerEl.querySelectorAll('.option');

    // Show the correct answer
    if (options[question.correctOption]) {
        options[question.correctOption].classList.add('correct');
    }

    // Remove any existing feedback messages
    const existingMessages = document.querySelectorAll('.point-message, .no-point-message, .answer-message, .timer-message');
    existingMessages.forEach(msg => msg.remove());

    // We don't need to add a message here since we want to keep the green "Correct Answer!" text
    // that was added when the user got the answer right

    // Convert pause button back to next question button
    const nextBtn = document.getElementById('nextQuestionBtn');
    nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
    nextBtn.classList.remove('pause-btn');
    nextBtn.disabled = false;
    nextBtn.style.display = 'inline-block';

    // Remove any existing click event listeners by cloning the button
    nextBtn.replaceWith(nextBtn.cloneNode(true));

    // Get the fresh button reference after cloning
    const freshNextBtn = document.getElementById('nextQuestionBtn');

    // Add the next question functionality
    freshNextBtn.addEventListener('click', nextQuestion);

    // Ensure start timer button is hidden
    startTimerBtn.style.display = 'none';

    // Remove the paused visual indication if it exists
    timerEl.parentElement.classList.remove('paused');

    // Hide the reveal button
    const revealBtn = document.getElementById('revealAnswerBtn');
    if (revealBtn) {
        revealBtn.remove();
    }
}

// Move to next question
function nextQuestion() {
    // Only stop the timer audio if we're moving to the next question before it finishes naturally
    // This allows the audio to play to completion even when moving to the next question
    if (!timerAudio.ended) {
        timerAudio.pause();
        timerAudio.currentTime = 0;
    }

    // Restart background music for the next question
    playBackgroundMusic();

    // Move to next team
    currentQuizState.currentTeamIndex++;
    if (currentQuizState.currentTeamIndex >= teams.length) {
        currentQuizState.currentTeamIndex = 0;
    }

    // Update available questions for the next team
    updateAvailableQuestions();

    // Update team score display for the new team
    updateTeamScoreDisplay();

    // Save state after updating team index and available questions
    saveQuizState();

    // Check if there are any questions left for this round
    if (currentQuizState.availableQuestions.length === 0) {
        // No more questions for this round
        endQuiz();
        return;
    }

    // Clear the timer if it's still running
    clearInterval(currentQuizState.timer);

    // Reset timer state
    currentQuizState.timeLeft = currentSettings.timerDuration;

    // Load next question
    loadQuestion();
}

// End the current round and show round selection
function endQuiz() {
    // Only stop the timer audio if we're ending the quiz before it finishes naturally
    // This allows the audio to play to completion even when ending the quiz
    if (!timerAudio.ended) {
        timerAudio.pause();
        timerAudio.currentTime = 0;
    }

    // Stop background music
    stopBackgroundMusic();

    // Clear any existing timer
    clearInterval(currentQuizState.timer);

    // Mark the current round as completed
    if (!currentQuizState.completedRounds.includes(currentQuizState.currentRoundIndex)) {
        currentQuizState.completedRounds.push(currentQuizState.currentRoundIndex);
    }

    // Make sure the score array is properly initialized
    if (!currentQuizState.score || currentQuizState.score.length !== teams.length) {
        currentQuizState.score = teams.map(() => 0);
    }

    // Ensure all scores are valid numbers
    for (let i = 0; i < currentQuizState.score.length; i++) {
        if (isNaN(currentQuizState.score[i]) || currentQuizState.score[i] === undefined) {
            currentQuizState.score[i] = 0;
        }
    }

    // CRITICAL FIX: Completely clear the quiz state from localStorage
    // This prevents the old round from being restored when selecting a new round
    localStorage.removeItem('bibleQuizState');
    localStorage.removeItem('bibleQuizProgress');

    // Reset all quiz state to defaults
    currentQuizState = {
        currentQuestionIndex: 0,
        currentTeamIndex: 0,
        currentRoundIndex: -1, // Set to -1 to indicate no round selected
        currentRoundQuestionNumber: 0,
        score: currentQuizState.score, // Keep the scores
        timer: null,
        timeLeft: currentSettings.timerDuration,
        questionAnswered: false,
        timerPaused: false,
        teamQuestions: teams.map(() => []),
        availableQuestions: [],
        usedQuestions: [],
        roundsCompleted: false,
        completedRounds: currentQuizState.completedRounds, // Keep completed rounds
        roundQuestions: [],
        currentRoundType: "questions",
        anagramState: {
            currentAnagramIndex: 0,
            clickedLetters: [],
            correctSequence: [],
            anagramCompleted: false,
            firstAttempt: true
        }
    };

    // Set quiz as not in progress
    quizInProgress = false;
    quizPaused = false;

    // Hide quiz section and show round selection section
    quizSection.classList.add('hidden');
    menuSection.classList.add('hidden');
    scoresSection.classList.add('hidden');
    roundSelectionSection.classList.remove('hidden');

    // Render the available rounds
    renderRoundSelection();

    // If all rounds are completed, show a message or handle accordingly
    if (currentQuizState.completedRounds.length === rounds.length) {
        // All rounds completed - could show a message or handle differently
        // For now, we'll just let them select any round to replay
    }

    console.log("Quiz ended, state cleared, ready for new round selection");
}

// Start a new quiz
function newQuiz() {
    // Reset all quiz state
    currentQuizState.currentQuestionIndex = 0;
    currentQuizState.currentTeamIndex = 0;
    currentQuizState.currentRoundIndex = 0;
    currentQuizState.currentRoundQuestionNumber = 0;
    currentQuizState.score = teams.map(() => 0);
    currentQuizState.questionAnswered = false;
    currentQuizState.timerPaused = false;
    currentQuizState.roundsCompleted = false;
    currentQuizState.completedRounds = [];
    currentQuizState.usedQuestions = []; // Clear used questions
    currentQuizState.teamQuestions = teams.map(() => []); // Reset team questions

    // Reset quiz progress tracking
    quizInProgress = false;
    quizPaused = false;

    // Hide all sections and show menu
    resultsSection.classList.add('hidden');
    teamsSection.classList.add('hidden');
    roundSelectionSection.classList.add('hidden');
    quizSection.classList.add('hidden');
    menuSection.classList.remove('hidden');

    // Hide restart button if needed
    restartQuizBtn.classList.add('hidden');

    // Save the reset state
    saveQuizState();
}

// Go to round selection from results
function goToRoundSelection() {
    // CRITICAL FIX: Completely clear the quiz state from localStorage
    // This prevents the old round from being restored when selecting a new round
    localStorage.removeItem('bibleQuizState');
    localStorage.removeItem('bibleQuizProgress');

    // Reset all quiz state to defaults (keep scores and completed rounds)
    const currentScores = currentQuizState.score || teams.map(() => 0);
    const completedRounds = currentQuizState.completedRounds || [];

    currentQuizState = {
        currentQuestionIndex: 0,
        currentTeamIndex: 0,
        currentRoundIndex: -1, // Set to -1 to indicate no round selected
        currentRoundQuestionNumber: 0,
        score: currentScores,
        timer: null,
        timeLeft: currentSettings.timerDuration,
        questionAnswered: false,
        timerPaused: false,
        teamQuestions: teams.map(() => []),
        availableQuestions: [],
        usedQuestions: [],
        roundsCompleted: false,
        completedRounds: completedRounds,
        roundQuestions: [],
        currentRoundType: "questions",
        anagramState: {
            currentAnagramIndex: 0,
            clickedLetters: [],
            correctSequence: [],
            anagramCompleted: false,
            firstAttempt: true
        }
    };

    // Set quiz as not in progress to prevent continuation logic
    quizInProgress = false;
    quizPaused = false;

    console.log("Going to round selection, completely reset state");

    // Hide all sections and show round selection section
    menuSection.classList.add('hidden');
    teamsSection.classList.add('hidden');
    quizSection.classList.add('hidden');
    resultsSection.classList.add('hidden');
    roundSelectionSection.classList.remove('hidden');

    // Render the available rounds
    renderRoundSelection();
}

// Go back to teams from round selection
function goBackToTeams() {
    // Hide round selection section and show teams section
    roundSelectionSection.classList.add('hidden');
    document.querySelector('.teams-section').classList.remove('hidden');
}

// Continue quiz from where it was left off
function continueQuiz() {
    if (quizInProgress) {
        // Hide menu and show quiz section
        menuSection.classList.add('hidden');
        quizSection.classList.remove('hidden');

        // Play background music when continuing the quiz
        playBackgroundMusic();

        // Make sure we have the current round loaded
        if (currentQuizState.roundQuestions.length === 0 && rounds.length > 0 && currentQuizState.currentRoundIndex < rounds.length) {
            // Reload the current round
            loadRound(currentQuizState.currentRoundIndex);
        }

        // Check if we need to reload the current question
        const questionTextEl = document.getElementById('questionText');
        if (questionTextEl && (questionTextEl.textContent === 'Loading question...' || questionTextEl.textContent === '')) {
            // We need to reload the current question

            // First, make sure we have valid team questions
            if (!currentQuizState.teamQuestions ||
                !currentQuizState.teamQuestions[currentQuizState.currentTeamIndex] ||
                currentQuizState.teamQuestions[currentQuizState.currentTeamIndex].length === 0) {

                // If no questions have been loaded yet, load a new one
                updateAvailableQuestions();
                loadQuestion();
            } else {
                // Get the current question index from the team's question list
                const teamQuestions = currentQuizState.teamQuestions[currentQuizState.currentTeamIndex];
                const currentQuestionIndex = teamQuestions[teamQuestions.length - 1];

                if (currentQuizState.roundQuestions[currentQuestionIndex]) {
                    // We have a valid question, display it
                    const question = currentQuizState.roundQuestions[currentQuestionIndex];
                    const team = teams[currentQuizState.currentTeamIndex];

                    // Update UI
                    questionTextEl.textContent = question.text;
                    currentTeamEl.textContent = team.name;

                    // Display question image if available
                    const existingImage = document.querySelector('.question-image');
                    const existingImageContainer = document.querySelector('.question-image-container');
                    const existingImageButton = document.querySelector('.image-preview-button');

                    if (existingImage) {
                        existingImage.remove();
                    }
                    if (existingImageContainer) {
                        existingImageContainer.remove();
                    }
                    if (existingImageButton) {
                        existingImageButton.remove();
                    }

                    if (question.image) {
                        // Create image container
                        const imageContainer = document.createElement('div');
                        imageContainer.className = 'question-image-container';

                        // Create image element
                        const imageElement = document.createElement('img');
                        imageElement.src = `images/${question.image}`;
                        imageElement.alt = 'Question Image';
                        imageElement.className = 'question-image';

                        // Add click event to open modal
                        imageElement.addEventListener('click', () => openImagePreviewModal(question.image));

                        // Create preview button
                        const previewButton = document.createElement('button');
                        previewButton.className = 'image-preview-button';
                        previewButton.innerHTML = '<i class="fas fa-expand"></i> View Image';
                        previewButton.addEventListener('click', () => openImagePreviewModal(question.image));

                        // Add image to container
                        imageContainer.appendChild(imageElement);

                        // Add preview button to container (below the image)
                        const buttonContainer = document.createElement('div');
                        buttonContainer.style.textAlign = 'center';
                        buttonContainer.style.marginTop = '10px';
                        buttonContainer.appendChild(previewButton);
                        imageContainer.appendChild(buttonContainer);

                        // Insert the container after the question text
                        questionTextEl.parentNode.insertBefore(imageContainer, questionTextEl.nextSibling);
                    }

                    // Update the current question number
                    if (currentQuestionEl) {
                        currentQuestionEl.textContent = currentQuizState.currentRoundQuestionNumber;
                    }

                    // Update the total questions count
                    if (totalQuestionsEl) {
                        totalQuestionsEl.textContent = currentQuizState.roundQuestions.length;
                    }

                    // Update round display
                    if (currentRoundEl) {
                        currentRoundEl.textContent = rounds[currentQuizState.currentRoundIndex].name;
                    }

                    // Update team score display
                    updateTeamScoreDisplay();

                    // Check if the question has already been answered
                    if (currentQuizState.questionAnswered) {
                        // If already answered, show options with the correct answer highlighted
                        optionsContainerEl.innerHTML = '';

                        // Add options with correct/incorrect classes
                        question.options.forEach((option, index) => {
                            const optionEl = document.createElement('div');
                            optionEl.className = 'option';
                            if (index === question.correctOption) {
                                optionEl.classList.add('correct');
                            }
                            optionEl.textContent = option;
                            optionsContainerEl.appendChild(optionEl);
                        });

                        // Show next question button
                        const nextBtn = document.getElementById('nextQuestionBtn');
                        nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
                        nextBtn.classList.remove('pause-btn');
                        nextBtn.disabled = false;
                        nextBtn.style.display = 'inline-block';

                        // Remove any existing click event listeners
                        nextBtn.replaceWith(nextBtn.cloneNode(true));

                        // Get the fresh button reference after cloning
                        const freshNextBtn = document.getElementById('nextQuestionBtn');

                        // Add the next question functionality
                        freshNextBtn.addEventListener('click', nextQuestion);

                        // Hide start timer button
                        document.getElementById('startTimerBtn').style.display = 'none';
                    } else {
                        // If not answered yet, show start timer button
                        document.getElementById('startTimerBtn').style.display = 'inline-block';

                        // If options are already displayed, make sure they're clickable
                        if (optionsContainerEl.children.length > 0) {
                            // Re-add click event listeners to options
                            Array.from(optionsContainerEl.children).forEach((option, index) => {
                                // Remove existing listeners by cloning
                                const newOption = option.cloneNode(true);
                                option.parentNode.replaceChild(newOption, option);

                                // Add new listener
                                newOption.addEventListener('click', () => selectOption(index));
                            });
                        } else {
                            // Clear options container to prepare for new options
                            optionsContainerEl.innerHTML = '';
                        }

                        // Reset timer display
                        clearInterval(currentQuizState.timer);
                        currentQuizState.timeLeft = currentSettings.timerDuration;
                        timerEl.textContent = currentQuizState.timeLeft;
                        timerEl.parentElement.style.setProperty('--progress', '100%');
                    }
                } else {
                    // Something is wrong with the saved state, load a new question
                    updateAvailableQuestions();
                    loadQuestion();
                }
            }
        }

        // If quiz was paused, resume it
        if (quizPaused) {
            quizPaused = false;
        }
    }
}

// Restart quiz (reset scores but keep teams)
function restartQuiz() {
    if (confirm('Are you sure you want to restart the quiz? All scores will be reset.')) {
        // Reset scores and quiz state but keep teams
        currentQuizState.score = teams.map(() => 0);
        currentQuizState.completedRounds = [];
        currentQuizState.currentRoundIndex = 0;
        currentQuizState.currentTeamIndex = 0;
        currentQuizState.currentRoundQuestionNumber = 0;
        currentQuizState.usedQuestions = [];
        currentQuizState.teamQuestions = teams.map(() => []);
        currentQuizState.questionAnswered = false;
        currentQuizState.timerPaused = false;

        // Reset available questions
        if (rounds.length > 0) {
            currentQuizState.roundQuestions = [...rounds[0].questions];
            if (currentSettings.shuffleQuestions) {
                currentQuizState.roundQuestions = shuffleArray(currentQuizState.roundQuestions);
            }
            currentQuizState.availableQuestions = [...Array(currentQuizState.roundQuestions.length).keys()];
        }

        // Update quiz progress tracking
        quizInProgress = true;
        quizPaused = false;

        // Save the updated state
        saveQuizState();

        // Show round selection to start fresh
        menuSection.classList.add('hidden');
        roundSelectionSection.classList.remove('hidden');

        // Render the available rounds
        renderRoundSelection();
    }
}



// Open add/edit question modal
function openAddQuestionModal() {
    // Check if there are any rounds
    if (rounds.length === 0) {
        alert('Please add a round first before adding questions.');
        return;
    }

    // Get the selected round index
    const roundIndex = parseInt(questionRoundSelect.value);

    // If invalid selection, show message
    if (roundIndex < 0 || roundIndex >= rounds.length) {
        alert('Please select a valid round before adding questions.');
        return;
    }

    questionInput.value = '';
    option1Input.value = '';
    option2Input.value = '';
    option3Input.value = '';
    option4Input.value = '';
    document.getElementById('option1Radio').checked = true;
    
    // Reset question image selection
    const questionImageSelect = document.getElementById('questionImageSelect');
    if (questionImageSelect) {
        questionImageSelect.value = '';
        // Clear the image preview
        const imagePreview = document.getElementById('questionImagePreview');
        imagePreview.innerHTML = '';
    }

    // Populate the question image dropdown
    populateQuestionImageDropdown();

    questionModalTitle.textContent = 'Add New Question';
    saveQuestionBtn.dataset.mode = 'add';
    saveQuestionBtn.dataset.index = '';
    saveQuestionBtn.dataset.roundIndex = roundIndex;

    addQuestionModal.classList.add('active');
}

function openEditQuestionModal(index, roundIndex) {
    const question = rounds[roundIndex].questions[index];

    questionModalTitle.textContent = 'Edit Question';
    questionInput.value = question.text;
    option1Input.value = question.options[0];
    option2Input.value = question.options[1];
    option3Input.value = question.options[2];
    option4Input.value = question.options[3];

    document.getElementById(`option${question.correctOption + 1}Radio`).checked = true;

    // Populate the question image dropdown
    populateQuestionImageDropdown();
    
    // Set the selected image if it exists
    const questionImageSelect = document.getElementById('questionImageSelect');
    if (questionImageSelect && question.image) {
        questionImageSelect.value = question.image;
        
        // Update the image preview
        const imagePreview = document.getElementById('questionImagePreview');
        if (imagePreview && question.image) {
            imagePreview.innerHTML = `<img src="images/${question.image}" alt="Question Image">`;
        } else {
            imagePreview.innerHTML = '';
        }
    } else if (questionImageSelect) {
        questionImageSelect.value = '';
        // Clear the image preview
        const imagePreview = document.getElementById('questionImagePreview');
        imagePreview.innerHTML = '';
    }

    saveQuestionBtn.dataset.mode = 'edit';
    saveQuestionBtn.dataset.index = index;
    saveQuestionBtn.dataset.roundIndex = roundIndex;

    addQuestionModal.classList.add('active');
}

// Save question
function saveQuestion() {
    const text = questionInput.value.trim();
    const option1 = option1Input.value.trim();
    const option2 = option2Input.value.trim();
    const option3 = option3Input.value.trim();
    const option4 = option4Input.value.trim();
    const correctOption = parseInt(document.querySelector('input[name="correctOption"]:checked').value);
    const roundIndex = parseInt(saveQuestionBtn.dataset.roundIndex);
    const questionImage = document.getElementById('questionImageSelect').value;

    if (!text || !option1 || !option2 || !option3 || !option4) {
        alert('Please fill in all fields.');
        return;
    }

    if (roundIndex < 0 || roundIndex >= rounds.length) {
        alert('Invalid round selection.');
        return;
    }

    const question = {
        text,
        options: [option1, option2, option3, option4],
        correctOption,
        image: questionImage // Add the image property
    };

    const mode = saveQuestionBtn.dataset.mode;
    if (mode === 'add') {
        rounds[roundIndex].questions.push(question);
    } else {
        const index = parseInt(saveQuestionBtn.dataset.index);
        rounds[roundIndex].questions[index] = question;
    }

    saveData();
    renderRoundsList();
    renderQuestionsList();
    addQuestionModal.classList.remove('active');
}

// ===== ANAGRAM MANAGEMENT FUNCTIONS =====

// Open add anagram modal
function openAddAnagramModal() {
    // Clear form
    const wordInput = document.getElementById('anagramWordInput');
    if (wordInput) {
        wordInput.value = '';
        // Add event listener for word input changes
        wordInput.removeEventListener('input', handleWordInputChange);
        wordInput.addEventListener('input', handleWordInputChange);
    }

    // Clear containers
    clearHintPatternContainer();
    clearScrambledLettersContainer();

    // Set modal mode
    const saveAnagramBtn = document.getElementById('saveAnagramBtn');
    const selectedRoundIndex = document.getElementById('questionRoundSelect').value;

    if (saveAnagramBtn) {
        saveAnagramBtn.dataset.mode = 'add';
        saveAnagramBtn.dataset.roundIndex = selectedRoundIndex;
    }

    // Set modal title
    const modalTitle = document.getElementById('anagramModalTitle');
    if (modalTitle) modalTitle.textContent = 'Add New Anagram';

    const modal = document.getElementById('addAnagramModal');
    if (modal) {
        modal.classList.add('active');
    }

    // Initialize event listeners
    initializeAnagramModalEventListeners();
}

// Open edit anagram modal
function openEditAnagramModal(anagramIndex, roundIndex) {
    const round = rounds[roundIndex];
    const anagram = round.anagrams[anagramIndex];

    // Populate word input
    const wordInput = document.getElementById('anagramWordInput');
    if (wordInput) {
        wordInput.value = anagram.word;
        // Add event listener for word input changes
        wordInput.removeEventListener('input', handleWordInputChange);
        wordInput.addEventListener('input', handleWordInputChange);
    }

    // Generate hint pattern from existing data
    generateHintPatternFromWord(anagram.word, anagram.hintLetters);

    // Generate scrambled letters from existing data
    const availableOrder = anagram.availableLettersOrder || anagram.randomLetters;
    generateScrambledLettersFromArray(availableOrder);

    // Set modal mode
    const saveAnagramBtn = document.getElementById('saveAnagramBtn');
    if (saveAnagramBtn) {
        saveAnagramBtn.dataset.mode = 'edit';
        saveAnagramBtn.dataset.roundIndex = roundIndex;
        saveAnagramBtn.dataset.anagramIndex = anagramIndex;
    }

    // Set modal title
    const modalTitle = document.getElementById('anagramModalTitle');
    if (modalTitle) modalTitle.textContent = 'Edit Anagram';

    const modal = document.getElementById('addAnagramModal');
    if (modal) {
        modal.classList.add('active');
    }

    // Initialize event listeners
    initializeAnagramModalEventListeners();
}

// ===== NEW ANAGRAM MODAL HELPER FUNCTIONS =====

// Handle word input changes
function handleWordInputChange() {
    const wordInput = document.getElementById('anagramWordInput');
    if (!wordInput) return;

    const word = wordInput.value.trim().toUpperCase();
    if (word) {
        generateHintPatternFromWord(word);
        updateScrambledLettersFromHintPattern();
    } else {
        clearHintPatternContainer();
        clearScrambledLettersContainer();
    }
}

// Clear hint pattern container
function clearHintPatternContainer() {
    const container = document.getElementById('hintPatternContainer');
    if (container) {
        container.innerHTML = '';
    }
}

// Clear scrambled letters container
function clearScrambledLettersContainer() {
    const container = document.getElementById('scrambledLettersContainer');
    if (container) {
        container.innerHTML = '';
    }
}

// Generate hint pattern from word
function generateHintPatternFromWord(word, existingHints = {}) {
    const container = document.getElementById('hintPatternContainer');
    if (!container) return;

    container.innerHTML = '';

    for (let i = 0; i < word.length; i++) {
        const letter = word[i];
        const position = i + 1;

        const letterElement = document.createElement('div');
        letterElement.className = 'hint-pattern-letter';
        letterElement.textContent = letter;
        letterElement.dataset.position = position;
        letterElement.dataset.letter = letter;

        // Set initial state based on existing hints
        if (existingHints[position]) {
            letterElement.classList.add('hint');
        } else {
            letterElement.classList.add('blank');
            letterElement.textContent = '_';
        }

        // Add click event listener
        letterElement.addEventListener('click', toggleHintLetter);

        container.appendChild(letterElement);
    }
}

// Toggle hint letter state
function toggleHintLetter(event) {
    const letterElement = event.target;
    const letter = letterElement.dataset.letter;

    if (letterElement.classList.contains('hint')) {
        // Change to blank
        letterElement.classList.remove('hint');
        letterElement.classList.add('blank');
        letterElement.textContent = '_';
    } else {
        // Change to hint
        letterElement.classList.remove('blank');
        letterElement.classList.add('hint');
        letterElement.textContent = letter;
    }

    // Update scrambled letters based on new hint pattern
    updateScrambledLettersFromHintPattern();
}

// Update scrambled letters based on hint pattern
function updateScrambledLettersFromHintPattern() {
    const hintPatternContainer = document.getElementById('hintPatternContainer');
    const scrambledContainer = document.getElementById('scrambledLettersContainer');
    if (!hintPatternContainer || !scrambledContainer) return;

    // Get current extra letters (preserve them)
    const currentExtraLetters = Array.from(scrambledContainer.querySelectorAll('.scrambled-letter.extra'))
        .map(el => ({ letter: el.dataset.letter, element: el }));

    // Get blank letters from hint pattern
    const blankLetters = [];
    const hintLetters = hintPatternContainer.querySelectorAll('.hint-pattern-letter.blank');

    hintLetters.forEach(letterElement => {
        blankLetters.push(letterElement.dataset.letter);
    });

    // Clear container and regenerate with blank letters
    scrambledContainer.innerHTML = '';

    // Add blank letters first
    blankLetters.forEach(letter => {
        createScrambledLetter(letter, false, scrambledContainer);
    });

    // Re-add extra letters
    currentExtraLetters.forEach(({ letter }) => {
        createScrambledLetter(letter, true, scrambledContainer);
    });
}

// Generate scrambled letters from array
function generateScrambledLettersFromArray(letters) {
    const container = document.getElementById('scrambledLettersContainer');
    if (!container) return;

    container.innerHTML = '';

    letters.forEach((letter, index) => {
        createScrambledLetter(letter, false, container);
    });
}

// Create a scrambled letter element
function createScrambledLetter(letter, isExtra = false, container = null) {
    if (!container) {
        container = document.getElementById('scrambledLettersContainer');
    }
    if (!container) return null;

    const letterElement = document.createElement('div');
    letterElement.className = 'scrambled-letter';
    letterElement.textContent = letter;
    letterElement.dataset.letter = letter;
    letterElement.draggable = true;

    if (isExtra) {
        letterElement.classList.add('extra');

        // Add remove button for extra letters
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-letter';
        removeBtn.innerHTML = '×';
        removeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            letterElement.remove();
        });
        letterElement.appendChild(removeBtn);
    }

    // Add drag event listeners
    letterElement.addEventListener('dragstart', handleDragStart);
    letterElement.addEventListener('dragend', handleDragEnd);

    container.appendChild(letterElement);
    return letterElement;
}

// Handle drag start
function handleDragStart(event) {
    event.dataTransfer.setData('text/plain', event.target.dataset.letter);
    event.target.classList.add('dragging');
}

// Handle drag end
function handleDragEnd(event) {
    event.target.classList.remove('dragging');
}

// Initialize anagram modal event listeners
function initializeAnagramModalEventListeners() {
    // Add extra letter button
    const addExtraLetterBtn = document.getElementById('addExtraLetterBtn');
    if (addExtraLetterBtn) {
        addExtraLetterBtn.removeEventListener('click', addExtraLetter);
        addExtraLetterBtn.addEventListener('click', addExtraLetter);
    }

    // Shuffle letters button
    const shuffleLettersBtn = document.getElementById('shuffleLettersBtn');
    if (shuffleLettersBtn) {
        shuffleLettersBtn.removeEventListener('click', shuffleScrambledLetters);
        shuffleLettersBtn.addEventListener('click', shuffleScrambledLetters);
    }

    // Make scrambled letters container droppable
    const scrambledContainer = document.getElementById('scrambledLettersContainer');
    if (scrambledContainer) {
        scrambledContainer.addEventListener('dragover', handleDragOver);
        scrambledContainer.addEventListener('drop', handleDrop);
    }
}

// Add extra letter
function addExtraLetter() {
    const letter = prompt('Enter a letter to add:');
    if (letter && letter.length === 1 && /[A-Z]/i.test(letter)) {
        const container = document.getElementById('scrambledLettersContainer');
        createScrambledLetter(letter.toUpperCase(), true, container);
    }
}

// Shuffle scrambled letters
function shuffleScrambledLetters() {
    const container = document.getElementById('scrambledLettersContainer');
    if (!container) return;

    const letters = Array.from(container.children);
    const shuffled = letters.sort(() => Math.random() - 0.5);

    container.innerHTML = '';
    shuffled.forEach(letter => container.appendChild(letter));
}

// Handle drag over
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('drag-over');

    // Find the element being dragged over
    const afterElement = getDragAfterElement(event.currentTarget, event.clientX);
    const draggedElement = document.querySelector('.dragging');

    if (afterElement == null) {
        event.currentTarget.appendChild(draggedElement);
    } else {
        event.currentTarget.insertBefore(draggedElement, afterElement);
    }
}

// Handle drop
function handleDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('drag-over');
}

// Get the element after which the dragged element should be inserted
function getDragAfterElement(container, x) {
    const draggableElements = [...container.querySelectorAll('.scrambled-letter:not(.dragging)')];

    return draggableElements.reduce((closest, child) => {
        const box = child.getBoundingClientRect();
        const offset = x - box.left - box.width / 2;

        if (offset < 0 && offset > closest.offset) {
            return { offset: offset, element: child };
        } else {
            return closest;
        }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
}

// Save anagram
function saveAnagram() {
    // Get form elements
    const wordInput = document.getElementById('anagramWordInput');
    const saveAnagramBtn = document.getElementById('saveAnagramBtn');
    const hintPatternContainer = document.getElementById('hintPatternContainer');
    const scrambledLettersContainer = document.getElementById('scrambledLettersContainer');

    if (!wordInput || !saveAnagramBtn || !hintPatternContainer || !scrambledLettersContainer) {
        alert('Error: Form elements not found. Please refresh the page and try again.');
        return;
    }

    const word = wordInput.value.trim().toUpperCase();
    const roundIndex = parseInt(saveAnagramBtn.dataset.roundIndex);

    if (!word) {
        alert('Please enter a word.');
        return;
    }

    if (roundIndex < 0 || roundIndex >= rounds.length) {
        alert('Invalid round selection. Please select a valid anagram round.');
        return;
    }

    // Get hint letters from hint pattern
    const hintLetters = {};
    const hintPatternLetters = hintPatternContainer.querySelectorAll('.hint-pattern-letter.hint');
    hintPatternLetters.forEach(letterElement => {
        const position = parseInt(letterElement.dataset.position);
        const letter = letterElement.dataset.letter;
        hintLetters[position] = letter;
    });

    // Get scrambled letters from scrambled letters container
    const scrambledLetterElements = scrambledLettersContainer.querySelectorAll('.scrambled-letter');
    const availableLettersOrder = Array.from(scrambledLetterElements).map(el => el.dataset.letter);

    if (availableLettersOrder.length === 0) {
        alert('Please arrange some letters in the scrambled letters area.');
        return;
    }

    // Create correctSequence: letters in word order for blank positions only
    const correctSequence = [];
    const randomLetters = [];

    for (let i = 1; i <= word.length; i++) {
        // If this position is NOT a hint letter, it's a blank that needs to be filled
        if (!hintLetters[i]) {
            const letter = word[i - 1]; // Convert to 0-based index
            correctSequence.push(letter);
            randomLetters.push(letter);
        }
    }

    // Validate that availableLettersOrder contains the same letters as randomLetters
    const availableLettersSorted = [...availableLettersOrder].sort();
    const randomLettersSorted = [...randomLetters].sort();

    if (availableLettersSorted.join('') !== randomLettersSorted.join('')) {
        alert(`Error: The scrambled letters don't match the blank positions in the word.\n\nExpected letters: ${randomLetters.join(', ')}\nScrambled letters: ${availableLettersOrder.join(', ')}\n\nPlease make sure you only have the letters that correspond to blank positions in your hint pattern.`);
        return;
    }

    // Create anagram object
    const anagram = {
        word: word,
        hintLetters: hintLetters,
        randomLetters: randomLetters, // Letters that need to be filled (in word order)
        correctSequence: correctSequence, // Same as randomLetters - the order they should be clicked
        availableLettersOrder: availableLettersOrder // The scrambled order for display
    };

    // Get the round
    const round = rounds[roundIndex];
    if (round.type !== 'anagrams') {
        alert('Selected round is not an anagram round.');
        return;
    }

    if (!round.anagrams) {
        round.anagrams = [];
    }

    // Check if we're editing or adding
    const mode = saveAnagramBtn.dataset.mode;

    if (mode === 'edit') {
        // Update existing anagram
        const anagramIndex = parseInt(saveAnagramBtn.dataset.anagramIndex);
        round.anagrams[anagramIndex] = anagram;
    } else {
        // Add new anagram
        round.anagrams.push(anagram);
    }

    saveData();
    renderRoundsList();
    renderQuestionsList();
    document.getElementById('addAnagramModal').classList.remove('active');
}

// Open add/edit team modal
function openAddTeamModal() {
    teamNameInput.value = '';
    teamBackgroundColorInput.value = ''; // Clear background color input
    participantsContainer.innerHTML = `
        <div class="participant-input">
            <div class="participant-name-container">
                <select class="participant-name-select">
                    <option value="">Select a name...</option>
                </select>
                <input type="text" class="participant-name-input" placeholder="Enter new name" style="display: none;">
                <button class="add-name-btn" style="display: none;"><i class="fas fa-check"></i></button>
                <button class="cancel-name-btn" style="display: none;"><i class="fas fa-times"></i></button>
            </div>
            <select class="participant-profile-image">
                <option value="">No Image</option>
            </select>
            <button class="remove-participant-btn"><i class="fas fa-times"></i></button>
        </div>
    `;

    // Populate dropdowns
    populateParticipantNameDropdowns();
    populateProfileImageDropdowns();

    saveTeamBtn.dataset.mode = 'add';
    saveTeamBtn.dataset.index = '';

    addTeamModal.classList.add('active');

    // Add event listeners
    addRemoveParticipantListeners();
    addParticipantNameListeners();
}

function openEditTeamModal(index) {
    const team = teams[index];

    teamNameInput.value = team.name;
    teamBackgroundColorInput.value = team.backgroundColor || ''; // Populate background color input

    participantsContainer.innerHTML = team.participants.map(participant => `
        <div class="participant-input">
            <div class="participant-name-container">
                <select class="participant-name-select">
                    <option value="">Select a name...</option>
                    ${participantNames.map(name => `<option value="${name}" ${participant.name === name ? 'selected' : ''}>${name}</option>`).join('')}
                    <option value="__ADD_NEW__">+ Add new name...</option>
                </select>
                <input type="text" class="participant-name-input" placeholder="Enter new name" style="display: none;">
                <button class="add-name-btn" style="display: none;"><i class="fas fa-check"></i></button>
                <button class="cancel-name-btn" style="display: none;"><i class="fas fa-times"></i></button>
            </div>
            <select class="participant-profile-image">
                <option value="">No Image</option>
                ${profileImages.map(image => `<option value="${image}" ${participant.profileImage === image ? 'selected' : ''}>${image}</option>`).join('')}
            </select>
            <button class="remove-participant-btn"><i class="fas fa-times"></i></button>
        </div>
    `).join('');

    saveTeamBtn.dataset.mode = 'edit';
    saveTeamBtn.dataset.index = index;

    addTeamModal.classList.add('active');

    // Add event listeners
    addRemoveParticipantListeners();
    addParticipantNameListeners();
}

// Add event listeners to participant name dropdowns
function addParticipantNameListeners() {
    participantsContainer.querySelectorAll('.participant-name-select').forEach(select => {
        select.addEventListener('change', function() {
            if (this.value === '__ADD_NEW__') {
                // Show the input field for new name
                const container = this.parentElement;
                const nameInput = container.querySelector('.participant-name-input');
                const addBtn = container.querySelector('.add-name-btn');
                const cancelBtn = container.querySelector('.cancel-name-btn');

                this.style.display = 'none';
                nameInput.style.display = 'inline-block';
                addBtn.style.display = 'inline-block';
                cancelBtn.style.display = 'inline-block';
                nameInput.focus();
            }
        });
    });

    participantsContainer.querySelectorAll('.add-name-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const container = this.parentElement;
            const nameInput = container.querySelector('.participant-name-input');
            const select = container.querySelector('.participant-name-select');
            const cancelBtn = container.querySelector('.cancel-name-btn');

            const newName = nameInput.value.trim();
            if (newName) {
                if (addParticipantName(newName)) {
                    // Name added successfully, update all dropdowns
                    populateParticipantNameDropdowns();
                    // Set the new name as selected in this dropdown
                    select.value = newName;
                } else {
                    alert('This name already exists or is invalid.');
                }
            }

            // Hide input and show dropdown
            nameInput.style.display = 'none';
            this.style.display = 'none';
            cancelBtn.style.display = 'none';
            select.style.display = 'inline-block';
            nameInput.value = '';
        });
    });

    participantsContainer.querySelectorAll('.cancel-name-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const container = this.parentElement;
            const nameInput = container.querySelector('.participant-name-input');
            const select = container.querySelector('.participant-name-select');
            const addBtn = container.querySelector('.add-name-btn');

            // Reset to original state
            nameInput.style.display = 'none';
            addBtn.style.display = 'none';
            this.style.display = 'none';
            select.style.display = 'inline-block';
            select.value = '';
            nameInput.value = '';
        });
    });
}

// Add event listeners to participant name elements for a specific element
function addParticipantNameListenersForElement(element) {
    const select = element.querySelector('.participant-name-select');
    const nameInput = element.querySelector('.participant-name-input');
    const addBtn = element.querySelector('.add-name-btn');
    const cancelBtn = element.querySelector('.cancel-name-btn');

    select.addEventListener('change', function() {
        if (this.value === '__ADD_NEW__') {
            // Show the input field for new name
            this.style.display = 'none';
            nameInput.style.display = 'inline-block';
            addBtn.style.display = 'inline-block';
            cancelBtn.style.display = 'inline-block';
            nameInput.focus();
        }
    });

    addBtn.addEventListener('click', function() {
        const newName = nameInput.value.trim();
        if (newName) {
            if (addParticipantName(newName)) {
                // Name added successfully, update all dropdowns
                populateParticipantNameDropdowns();
                // Set the new name as selected in this dropdown
                select.value = newName;
            } else {
                alert('This name already exists or is invalid.');
            }
        }

        // Hide input and show dropdown
        nameInput.style.display = 'none';
        this.style.display = 'none';
        cancelBtn.style.display = 'none';
        select.style.display = 'inline-block';
        nameInput.value = '';
    });

    cancelBtn.addEventListener('click', function() {
        // Reset to original state
        nameInput.style.display = 'none';
        addBtn.style.display = 'none';
        this.style.display = 'none';
        select.style.display = 'inline-block';
        select.value = '';
        nameInput.value = '';
    });
}

// Add event listeners to remove participant buttons
function addRemoveParticipantListeners() {
    participantsContainer.querySelectorAll('.remove-participant-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const participantInputs = participantsContainer.querySelectorAll('.participant-input');
            if (participantInputs.length > 1) {
                this.parentElement.remove();
            } else {
                alert('Each team must have at least one participant.');
            }
        });
    });
}

// Add new participant input
function addParticipantInput() {
    const participantInput = document.createElement('div');
    participantInput.className = 'participant-input';
    participantInput.innerHTML = `
        <div class="participant-name-container">
            <select class="participant-name-select">
                <option value="">Select a name...</option>
            </select>
            <input type="text" class="participant-name-input" placeholder="Enter new name" style="display: none;">
            <button class="add-name-btn" style="display: none;"><i class="fas fa-check"></i></button>
            <button class="cancel-name-btn" style="display: none;"><i class="fas fa-times"></i></button>
        </div>
        <select class="participant-profile-image">
            <option value="">No Image</option>
        </select>
        <button class="remove-participant-btn"><i class="fas fa-times"></i></button>
    `;

    participantsContainer.appendChild(participantInput);

    // Populate the dropdowns for this specific participant
    const nameSelect = participantInput.querySelector('.participant-name-select');
    const imageSelect = participantInput.querySelector('.participant-profile-image');

    // Populate name dropdown
    participantNames.forEach(name => {
        const option = document.createElement('option');
        option.value = name;
        option.textContent = name;
        nameSelect.appendChild(option);
    });
    const addNewOption = document.createElement('option');
    addNewOption.value = '__ADD_NEW__';
    addNewOption.textContent = '+ Add new name...';
    nameSelect.appendChild(addNewOption);

    // Populate image dropdown
    populateProfileImageDropdown(imageSelect);

    // Add event listeners for this specific participant
    addParticipantNameListenersForElement(participantInput);

    // Add event listener to remove button
    participantInput.querySelector('.remove-participant-btn').addEventListener('click', function() {
        const participantInputs = participantsContainer.querySelectorAll('.participant-input');
        if (participantInputs.length > 1) {
            this.parentElement.remove();
        } else {
            alert('Each team must have at least one participant.');
        }
    });
}

// Save team
function saveTeam() {
    const name = teamNameInput.value.trim();
    const backgroundColor = teamBackgroundColorInput.value.trim(); // Get background color
    const participantInputs = participantsContainer.querySelectorAll('.participant-input');
    const participants = [];

    if (!name) {
        alert('Please enter a team name.');
        return;
    }

    participantInputs.forEach(div => {
        const nameSelect = div.querySelector('.participant-name-select');
        const imageSelect = div.querySelector('.participant-profile-image');
        const participantName = nameSelect ? nameSelect.value.trim() : '';
        const profileImage = imageSelect ? imageSelect.value : '';

        if (participantName && participantName !== '__ADD_NEW__') {
            participants.push({ name: participantName, profileImage: profileImage });
        }
    });

    if (participants.length === 0) {
        alert('Please add at least one participant.');
        return;
    }

    const team = {
        name,
        backgroundColor, // Add background color to the team object
        participants
    };

    const mode = saveTeamBtn.dataset.mode;
    if (mode === 'add') {
        teams.push(team);
    } else {
        const index = parseInt(saveTeamBtn.dataset.index);
        teams[index] = team;
    }

    saveData();
    renderTeams();
    renderSettingsTeamsList();
    addTeamModal.classList.remove('active');
}

// Save settings
function saveSettings() {
    const quizTitle = quizTitleInput.value.trim();
    const timerDuration = parseInt(timerDurationInput.value);
    const pointsPerCorrectAnswerInput = document.getElementById('pointsPerCorrectAnswer');
    const pointsPerCorrectAnswer = parseInt(pointsPerCorrectAnswerInput.value);
    const shuffleQuestions = shuffleQuestionsInput.checked;
    const titleAlignment = titleAlignmentInput.value;
    const titleSize = titleSizeInput.value;
    const questionAlignment = questionAlignmentInput.value;
    const questionSize = questionSizeInput.value;
    const timerSize = timerSizeInput.value;

    if (quizTitle === '') {
        alert('Please enter a quiz title.');
        return;
    }

    if (isNaN(timerDuration) || timerDuration < 5 || timerDuration > 60) {
        alert('Please enter a valid timer duration between 5 and 60 seconds.');
        return;
    }

    if (isNaN(pointsPerCorrectAnswer) || pointsPerCorrectAnswer < 1 || pointsPerCorrectAnswer > 1000) {
        alert('Please enter a valid points value between 1 and 1000.');
        return;
    }

    currentSettings.quizTitle = quizTitle;
    currentSettings.timerDuration = timerDuration;
    currentSettings.pointsPerCorrectAnswer = pointsPerCorrectAnswer;
    currentSettings.shuffleQuestions = shuffleQuestions;
    currentSettings.titleAlignment = titleAlignment;
    currentSettings.titleSize = titleSize;
    currentSettings.questionAlignment = questionAlignment;
    currentSettings.questionSize = questionSize;
    currentSettings.timerSize = timerSize;

    // Update the title in the header
    const titleElement = document.querySelector('header h1');
    if (titleElement) {
        titleElement.textContent = quizTitle;
    }

    // Apply the new styles
    applyTitleStyles();
    applyQuestionStyles();
    applyTimerStyles();

    saveData();
    settingsModal.classList.remove('active');
}

// Render score management interface
function renderScoreManagement() {
    const scoresManagementList = document.getElementById('scoresManagementList');
    if (!scoresManagementList) return;

    scoresManagementList.innerHTML = '';

    // Make sure we have a valid score array
    if (!currentQuizState.score || currentQuizState.score.length !== teams.length) {
        currentQuizState.score = teams.map(() => 0);
    }

    teams.forEach((team, index) => {
        const scoreItem = document.createElement('div');
        scoreItem.className = 'score-edit-item';

        scoreItem.innerHTML = `
            <div class="score-team-info">
                <div class="score-team-name">${team.name}</div>
                <div class="score-team-members">
                    ${team.participants.map(participant => `
                        <img src="${participant.profileImage ? 'profile_images/' + participant.profileImage : 'https://via.placeholder.com/30'}"
                             alt="${participant.name}"
                             class="score-member-avatar"
                             title="${participant.name}">
                    `).join('')}
                </div>
            </div>
            <div class="score-input-container">
                <input type="number"
                       class="score-input"
                       value="${currentQuizState.score[index] || 0}"
                       data-team-index="${index}"
                       data-original-value="${currentQuizState.score[index] || 0}"
                       min="0"
                       step="1">
                <span class="score-label">pts</span>
            </div>
        `;

        scoresManagementList.appendChild(scoreItem);
    });

    // Add event listeners to score inputs
    const scoreInputs = scoresManagementList.querySelectorAll('.score-input');
    scoreInputs.forEach(input => {
        input.addEventListener('input', handleScoreInputChange);
        input.addEventListener('blur', validateScoreInput);
    });
}

// Handle score input changes
function handleScoreInputChange(event) {
    const input = event.target;
    const originalValue = parseInt(input.dataset.originalValue);
    const currentValue = parseInt(input.value) || 0;

    // Add visual indicator if value has changed
    if (currentValue !== originalValue) {
        input.classList.add('changed');
    } else {
        input.classList.remove('changed');
    }
}

// Validate score input
function validateScoreInput(event) {
    const input = event.target;
    let value = parseInt(input.value);

    // Ensure value is a valid number and not negative
    if (isNaN(value) || value < 0) {
        value = 0;
    }

    input.value = value;
}

// Reset all scores to zero
function resetAllScores() {
    if (confirm('Are you sure you want to reset all team scores to 0? This action cannot be undone.')) {
        const scoreInputs = document.querySelectorAll('.score-input');
        scoreInputs.forEach(input => {
            input.value = 0;
            input.classList.add('changed');
        });
    }
}

// Save score changes
function saveScoreChanges() {
    const scoreInputs = document.querySelectorAll('.score-input');
    let hasChanges = false;

    scoreInputs.forEach(input => {
        const teamIndex = parseInt(input.dataset.teamIndex);
        const newScore = parseInt(input.value) || 0;
        const originalScore = parseInt(input.dataset.originalValue);

        if (newScore !== originalScore) {
            currentQuizState.score[teamIndex] = newScore;
            input.dataset.originalValue = newScore;
            input.classList.remove('changed');
            hasChanges = true;
        }
    });

    if (hasChanges) {
        // Update displayed scores to match
        displayedScores = [...currentQuizState.score];
        scoresNeedUpdate = false;

        // Save the changes
        saveQuizState();

        // Show success message
        alert('Team scores have been updated successfully!');

        // If we're currently viewing scores, update the display
        if (!scoresSection.classList.contains('hidden')) {
            renderScores();
        }
    } else {
        alert('No changes were made to the scores.');
    }
}

// Get the next question for the current team
function getNextQuestionForTeam() {
    if (currentQuizState.availableQuestions.length === 0) {
        return -1; // No more questions available
    }

    // Randomly select a question from available questions
    const randomIndex = Math.floor(Math.random() * currentQuizState.availableQuestions.length);
    const questionIndex = currentQuizState.availableQuestions[randomIndex];

    // Remove this question from available questions for this team
    currentQuizState.availableQuestions.splice(randomIndex, 1);

    return questionIndex;
}

// Update available questions for the current team
function updateAvailableQuestions() {
    currentQuizState.availableQuestions = [];

    // Handle different round types
    if (currentQuizState.currentRoundType === "anagrams") {
        // For anagrams, only include anagrams that haven't been used by any team in the current round
        for (let i = 0; i < currentQuizState.roundQuestions.length; i++) {
            // Check if this anagram index has been used in the current round
            const isUsedInCurrentRound = currentQuizState.usedQuestions.includes(i);

            if (!isUsedInCurrentRound) {
                currentQuizState.availableQuestions.push(i);
            }
        }
    } else {
        // For regular questions, use the existing logic
        for (let i = 0; i < currentQuizState.roundQuestions.length; i++) {
            // Check if this question index has been used in the current round
            const isUsedInCurrentRound = currentQuizState.usedQuestions.some(usedIndex => {
                // We need to compare the actual question text to identify the same question across rounds
                return currentQuizState.roundQuestions[i] &&
                       currentQuizState.roundQuestions[usedIndex] &&
                       currentQuizState.roundQuestions[i].text === currentQuizState.roundQuestions[usedIndex].text;
            });

            if (!isUsedInCurrentRound) {
                currentQuizState.availableQuestions.push(i);
            }
        }
    }

    console.log("Available questions updated:", currentQuizState.availableQuestions.length, "Round type:", currentQuizState.currentRoundType);
}

// Utility function to shuffle array
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

// Event Listeners
document.addEventListener('DOMContentLoaded', init);

// Quiz state tracking variables are declared at the top of the file

// Navigation functions
function showMenu() {
    // Only stop the timer audio if we're going to menu before it finishes naturally
    // This allows the audio to play to completion even when navigating to menu
    if (!timerAudio.ended) {
        timerAudio.pause();
        timerAudio.currentTime = 0;
    }

    // Stop background music
    stopBackgroundMusic();

    // Clear any existing timer
    clearInterval(currentQuizState.timer);

    // Hide all sections
    teamsSection.classList.add('hidden');
    roundSelectionSection.classList.add('hidden');
    quizSection.classList.add('hidden');
    resultsSection.classList.add('hidden');
    scoresSection.classList.add('hidden');

    // Show menu
    menuSection.classList.remove('hidden');

    // Show/hide continue and restart buttons based on quiz state
    if (quizInProgress) {
        continueQuizBtn.style.display = 'flex'; // Changed from inline-block to flex
        restartQuizBtn.classList.remove('hidden');
        startQuizBtn.style.display = 'none';
    } else {
        continueQuizBtn.style.display = 'none';
        restartQuizBtn.classList.add('hidden');
        startQuizBtn.style.display = 'inline-block';
    }
}

function showTeamsSection() {
    // Stop background music
    stopBackgroundMusic();

    // Hide all sections
    menuSection.classList.add('hidden');
    roundSelectionSection.classList.add('hidden');
    quizSection.classList.add('hidden');
    resultsSection.classList.add('hidden');

    // Show teams section
    teamsSection.classList.remove('hidden');
}

function showRoundsSection() {
    // Stop background music
    stopBackgroundMusic();

    // Hide all sections
    menuSection.classList.add('hidden');
    teamsSection.classList.add('hidden');
    quizSection.classList.add('hidden');
    resultsSection.classList.add('hidden');
    scoresSection.classList.add('hidden');

    // Show rounds section
    roundSelectionSection.classList.remove('hidden');

    // Render the available rounds
    renderRoundSelection();
}

function showScoresSection() {
    // Stop background music
    stopBackgroundMusic();

    // Hide all sections
    menuSection.classList.add('hidden');
    teamsSection.classList.add('hidden');
    quizSection.classList.add('hidden');
    resultsSection.classList.add('hidden');
    roundSelectionSection.classList.add('hidden');

    // Show scores section
    scoresSection.classList.remove('hidden');

    // Render the current scores
    renderScores();
    
    // Remove any navigation buttons that might have been added by endQuiz
    const scoresControls = document.querySelector('.scores-controls');
    const existingNextRoundBtn = document.getElementById('scoresNextRoundBtn');
    const existingNewQuizBtn = document.getElementById('scoresNewQuizBtn');
    const existingBackToMenuBtn = document.getElementById('scoresBackToMenuBtn');
    
    if (existingNextRoundBtn) existingNextRoundBtn.remove();
    if (existingNewQuizBtn) existingNewQuizBtn.remove();
    if (existingBackToMenuBtn) existingBackToMenuBtn.remove();
    
    // Reset the section title
    document.querySelector('.scores-section h2').textContent = 'Current Scores';
}

// Store the current displayed scores to track changes

// Render current scores (initial display without animations)
function renderScores() {
    const leaderboard = document.getElementById('scoresLeaderboard');
    const roundsSummary = document.getElementById('roundsSummary');

    // Clear the containers
    leaderboard.innerHTML = '';
    roundsSummary.innerHTML = '';

    // Make sure the score array is properly initialized
    if (!currentQuizState.score || currentQuizState.score.length !== teams.length) {
        currentQuizState.score = teams.map(() => 0);
    }

    // Ensure all scores are valid numbers
    for (let i = 0; i < currentQuizState.score.length; i++) {
        if (isNaN(currentQuizState.score[i]) || currentQuizState.score[i] === undefined) {
            currentQuizState.score[i] = 0;
        }
    }

    // Create team data with original indices
    const teamData = teams.map((team, index) => ({
        originalIndex: index,
        name: team.name,
        backgroundColor: team.backgroundColor,
        participants: team.participants,
        score: currentQuizState.score[index] || 0,
        displayedScore: displayedScores[index] || 0 // Use displayed score for initial render
    }));

    // Sort by displayed scores for initial positioning
    const sortedTeams = [...teamData].sort((a, b) => b.displayedScore - a.displayedScore);

    // Calculate max score for progress bars
    const maxScore = Math.max(...teamData.map(team => Math.max(team.score, team.displayedScore)), 1);

    // Render leaderboard items
    sortedTeams.forEach((team, position) => {
        const leaderboardItem = createLeaderboardItem(team, position, maxScore);
        leaderboard.appendChild(leaderboardItem);
    });

    // Store current displayed scores
    displayedScores = teamData.map(team => team.displayedScore);

    // Check if scores need updating
    scoresNeedUpdate = teamData.some(team => team.score !== team.displayedScore);

    // Add rounds summary
    rounds.forEach((round, index) => {
        const isCompleted = currentQuizState.completedRounds.includes(index);
        const roundStatus = document.createElement('div');
        roundStatus.className = `round-status ${isCompleted ? 'round-completed' : 'round-pending'}`;
        roundStatus.textContent = `${round.name}: ${isCompleted ? 'Completed' : 'Pending'}`;
        roundsSummary.appendChild(roundStatus);
    });
}

// Create a leaderboard item element
function createLeaderboardItem(team, position, maxScore) {
    const item = document.createElement('div');
    item.className = 'leaderboard-item';
    item.dataset.teamIndex = team.originalIndex;
    item.dataset.position = position;

    // Determine position class
    let positionClass = 'other';
    if (position === 0) positionClass = 'first';
    else if (position === 1) positionClass = 'second';
    else if (position === 2) positionClass = 'third';

    // Calculate progress percentage
    const progressPercentage = maxScore > 0 ? (team.displayedScore / maxScore) * 100 : 0;

    item.innerHTML = `
        <div class="leaderboard-position ${positionClass}">
            ${position + 1}
        </div>
        <div class="team-name-section">
            <div class="team-name-score">${team.name}</div>
        </div>
        <div class="progress-section">
            <div class="progress-bar-container">
                <div class="progress-bar" style="width: ${progressPercentage}%"></div>
            </div>
        </div>
        <div class="score-section">
            <div class="current-score">${team.displayedScore}</div>
            <div class="score-label">Points</div>
        </div>
        <div class="team-members-section">
            ${team.participants.map(participant => `
                <img src="${participant.profileImage ? 'profile_images/' + participant.profileImage : 'https://via.placeholder.com/45'}"
                     alt="${participant.name}"
                     class="member-avatar"
                     title="${participant.name}">
            `).join('')}
        </div>
    `;

    return item;
}

// Animated score update function
async function updateScoresWithAnimation() {
    const updateBtn = document.getElementById('updateScoresBtn');
    const leaderboard = document.getElementById('scoresLeaderboard');

    if (!scoresNeedUpdate) {
        // Trigger confetti even if no update needed
        triggerScoreConfetti();
        return;
    }

    // Update button state
    updateBtn.classList.add('updating');
    updateBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Updating...';
    updateBtn.disabled = true;

    try {
        // Create team data with current and target scores
        const teamData = teams.map((team, index) => ({
            originalIndex: index,
            name: team.name,
            backgroundColor: team.backgroundColor,
            participants: team.participants,
            currentDisplayedScore: displayedScores[index] || 0,
            targetScore: currentQuizState.score[index] || 0
        }));

        // Calculate max score for progress bars
        const maxScore = Math.max(...teamData.map(team => team.targetScore), 1);

        // Get current positions
        const currentItems = Array.from(leaderboard.children);
        const currentPositions = currentItems.map(item => ({
            element: item,
            teamIndex: parseInt(item.dataset.teamIndex),
            currentPosition: parseInt(item.dataset.position),
            rect: item.getBoundingClientRect()
        }));

        // Calculate target positions
        const targetOrder = [...teamData].sort((a, b) => b.targetScore - a.targetScore);

        // First animate score changes
        await animateScoreChanges(teamData, maxScore);

        // Then animate position movements with dragging effect
        await animatePositionMovements(currentPositions, targetOrder, leaderboard);

        // Update displayed scores
        displayedScores = teamData.map(team => team.targetScore);
        scoresNeedUpdate = false;

        // Trigger celebration
        triggerScoreConfetti();

    } finally {
        // Reset button state
        updateBtn.classList.remove('updating');
        updateBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Update Scores';
        updateBtn.disabled = false;
    }
}

// Animate score number changes and progress bars
function animateScoreChanges(teamData, maxScore) {
    return new Promise((resolve) => {
        const duration = 2000; // 2 seconds
        const startTime = Date.now();

        function updateScores() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Use easing function for smooth animation
            const easedProgress = 1 - Math.pow(1 - progress, 3); // Ease-out cubic

            teamData.forEach(team => {
                const item = document.querySelector(`[data-team-index="${team.originalIndex}"]`);
                if (item) {
                    const scoreDisplay = item.querySelector('.current-score');
                    const progressBar = item.querySelector('.progress-bar');

                    // Animate score number
                    const currentScore = Math.round(
                        team.currentDisplayedScore +
                        (team.targetScore - team.currentDisplayedScore) * easedProgress
                    );
                    scoreDisplay.textContent = currentScore;

                    // Animate progress bar
                    const progressPercentage = maxScore > 0 ? (currentScore / maxScore) * 100 : 0;
                    progressBar.style.width = `${progressPercentage}%`;
                }
            });

            if (progress < 1) {
                requestAnimationFrame(updateScores);
            } else {
                resolve();
            }
        }

        updateScores();
    });
}

// Animate position movements with dragging effect
function animatePositionMovements(currentPositions, targetOrder, leaderboard) {
    return new Promise((resolve) => {
        // Calculate movements needed
        const movements = [];

        currentPositions.forEach(current => {
            const targetPosition = targetOrder.findIndex(team => team.originalIndex === current.teamIndex);
            if (targetPosition !== current.currentPosition) {
                movements.push({
                    element: current.element,
                    teamIndex: current.teamIndex,
                    fromPosition: current.currentPosition,
                    toPosition: targetPosition,
                    startRect: current.rect
                });
            }
        });

        if (movements.length === 0) {
            resolve();
            return;
        }

        // Create placeholder elements and make moving elements absolute
        movements.forEach(movement => {
            // Create placeholder
            const placeholder = movement.element.cloneNode(true);
            placeholder.classList.add('placeholder');
            movement.element.parentNode.insertBefore(placeholder, movement.element);

            // Make original element absolute for animation
            movement.element.classList.add('moving');
            movement.element.style.position = 'absolute';
            movement.element.style.top = movement.startRect.top - leaderboard.getBoundingClientRect().top + 'px';
            movement.element.style.left = '20px';
            movement.element.style.right = '20px';
            movement.element.style.width = 'calc(100% - 40px)';
        });

        // Calculate target positions after DOM reorder
        setTimeout(() => {
            // Reorder placeholders to target positions
            const sortedTargetOrder = [...targetOrder];
            sortedTargetOrder.forEach((team, position) => {
                const placeholder = leaderboard.querySelector(`[data-team-index="${team.originalIndex}"].placeholder`);
                if (placeholder) {
                    // Update position data
                    placeholder.dataset.position = position;

                    // Update position badge
                    const positionBadge = placeholder.querySelector('.leaderboard-position');
                    positionBadge.textContent = position + 1;

                    // Update position class
                    positionBadge.className = 'leaderboard-position';
                    if (position === 0) positionBadge.classList.add('first');
                    else if (position === 1) positionBadge.classList.add('second');
                    else if (position === 2) positionBadge.classList.add('third');
                    else positionBadge.classList.add('other');

                    // Append to reorder
                    leaderboard.appendChild(placeholder);
                }
            });

            // Animate moving elements to their target positions
            const animationPromises = movements.map((movement, index) => {
                return new Promise((resolveMovement) => {
                    setTimeout(() => {
                        const targetPlaceholder = leaderboard.querySelector(`[data-team-index="${movement.teamIndex}"].placeholder`);
                        if (targetPlaceholder) {
                            const targetRect = targetPlaceholder.getBoundingClientRect();
                            const leaderboardRect = leaderboard.getBoundingClientRect();
                            const targetTop = targetRect.top - leaderboardRect.top;

                            // Update the moving element's position badge
                            const positionBadge = movement.element.querySelector('.leaderboard-position');
                            positionBadge.textContent = movement.toPosition + 1;

                            // Update position class
                            positionBadge.className = 'leaderboard-position';
                            if (movement.toPosition === 0) positionBadge.classList.add('first');
                            else if (movement.toPosition === 1) positionBadge.classList.add('second');
                            else if (movement.toPosition === 2) positionBadge.classList.add('third');
                            else positionBadge.classList.add('other');

                            // Animate to target position
                            movement.element.style.transition = 'top 1.5s cubic-bezier(0.4, 0, 0.2, 1)';
                            movement.element.style.top = targetTop + 'px';

                            setTimeout(() => {
                                // Replace placeholder with actual element
                                targetPlaceholder.parentNode.replaceChild(movement.element, targetPlaceholder);

                                // Reset element styles
                                movement.element.classList.remove('moving');
                                movement.element.style.position = '';
                                movement.element.style.top = '';
                                movement.element.style.left = '';
                                movement.element.style.right = '';
                                movement.element.style.width = '';
                                movement.element.style.transition = '';
                                movement.element.dataset.position = movement.toPosition;

                                resolveMovement();
                            }, 1500);
                        } else {
                            resolveMovement();
                        }
                    }, index * 100); // Stagger animations
                });
            });

            Promise.all(animationPromises).then(() => {
                /* ------------------------------------------------------------------
                   Ensure the final DOM order strictly follows the sorted targetOrder.
                   The previous logic only moved elements that changed rank, leaving
                   “non-moving” teams in their original DOM positions and causing the
                   overall order to be incorrect. By iterating over every team in
                   targetOrder and appending its real element (not the placeholder),
                   we achieve a canonical order for all rows.  We also update each
                   row’s data-attributes and position badge classes so subsequent
                   updates start from a correct baseline.
                ------------------------------------------------------------------ */
                targetOrder.forEach((team, pos) => {
                    const elem = leaderboard.querySelector(
                        `[data-team-index="${team.originalIndex}"]:not(.placeholder)`
                    );
                    if (elem) {
                        // Append in the desired order
                        leaderboard.appendChild(elem);

                        // Update cached position
                        elem.dataset.position = pos;

                        // Refresh badge content & styling
                        const badge = elem.querySelector('.leaderboard-position');
                        if (badge) {
                            badge.textContent = pos + 1;
                            badge.className = 'leaderboard-position';
                            if (pos === 0)      badge.classList.add('first');
                            else if (pos === 1) badge.classList.add('second');
                            else if (pos === 2) badge.classList.add('third');
                            else                badge.classList.add('other');
                        }
                    }
                });

                resolve();
            });
        }, 50);
    });
}

// Trigger confetti for score updates
function triggerScoreConfetti() {
    if (typeof confetti === 'undefined') {
        console.warn('Confetti library not loaded');
        return;
    }

    // Play fireworks sound
    fireworksAudio.currentTime = 0;
    fireworksAudio.play().catch(error => {
        console.error('Error playing fireworks audio:', error);
    });

    // Multiple bursts with different colors
    const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            confetti({
                particleCount: 100,
                spread: 70,
                origin: { y: 0.6 },
                colors: colors,
                zIndex: 3000
            });
        }, i * 300);
    }
}

// Main navigation event listeners are now added in the init() function

// Event listeners for these elements are now added in the init() function

// Modal event listeners are now added in the init() function

// Additional modal event listeners are now added in the init() function

// Team introduction modal
const teamIntroCloseBtn = document.getElementById('teamIntroCloseBtn');
const teamIntroductionModal = document.getElementById('teamIntroductionModal');

if (teamIntroCloseBtn) {
    teamIntroCloseBtn.addEventListener('click', closeTeamIntroduction);
}

// Close team introduction modal when clicking on overlay
if (teamIntroductionModal) {
    teamIntroductionModal.addEventListener('click', (e) => {
        if (e.target === teamIntroductionModal || e.target.classList.contains('team-intro-overlay')) {
            closeTeamIntroduction();
        }
    });
}

// Keyboard accessibility for team introduction modal
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && teamIntroductionModal && teamIntroductionModal.classList.contains('active')) {
        closeTeamIntroduction();
    }
});

// Close modals when clicking outside
window.addEventListener('click', (e) => {
    if (e.target === settingsModal) {
        settingsModal.classList.remove('active');
    } else if (e.target === addQuestionModal) {
        addQuestionModal.classList.remove('active');
    } else if (e.target === addTeamModal) {
        addTeamModal.classList.remove('active');
    } else if (e.target === document.getElementById('addAnagramModal')) {
        document.getElementById('addAnagramModal').classList.remove('active');
    }
});

// Function to load participant names from localStorage or initialize with defaults
function loadParticipantNames() {
    const savedNames = localStorage.getItem('bibleQuizParticipantNames');
    if (savedNames) {
        participantNames = JSON.parse(savedNames);
    } else {
        // Initialize with some default names
        participantNames = [
            "John", "Mary", "David", "Sarah", "Michael", "Rachel",
            "Daniel", "Rebecca", "Joshua", "Ruth", "Samuel", "Esther",
            "Matthew", "Hannah", "Luke", "Miriam", "Paul", "Deborah"
        ];
        // Save the default names
        localStorage.setItem('bibleQuizParticipantNames', JSON.stringify(participantNames));
    }
}

// Function to load profile image paths from the profile_images directory
function loadProfileImages() {
    // This function would typically fetch image names from a server or a predefined list.
    // For this exercise, we'll hardcode the list based on the environment_details.
    profileImages = [
        "APC Military Police Vehicle  Physics pack controller  available on the asset store 3.webp",
        "APC Military Police Vehicle  Physics pack controller  available on the asset store 25.webp",
        "APC Military Police Vehicle  Physics pack controller  available on the asset store 26.webp",
        "APC Military Police Vehicle  Physics pack controller  available on the asset store square.webp",
        "Armor Police Vehicle V221 game asset for Unity 2048x2048.webp",
        "New Update Terrain Land Ponds and Lake Summer Pack 3d game assets for Unity 2.webp",
        "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 04.webp",
        "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 07.webp",
        "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 09.webp",
        "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 13.webp",
        "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 17.webp",
        "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 19.webp"
    ];
}

// Function to add a new participant name to the list
function addParticipantName(name) {
    const trimmedName = name.trim();
    if (trimmedName && !participantNames.includes(trimmedName)) {
        participantNames.push(trimmedName);
        participantNames.sort(); // Keep names sorted alphabetically
        localStorage.setItem('bibleQuizParticipantNames', JSON.stringify(participantNames));
        return true;
    }
    return false;
}

// Function to populate participant name dropdowns
function populateParticipantNameDropdowns() {
    const dropdowns = document.querySelectorAll('.participant-name-select');
    dropdowns.forEach(dropdown => {
        const currentValue = dropdown.value;
        // Clear existing options
        dropdown.innerHTML = '<option value="">Select a name...</option>';

        // Add existing names
        participantNames.forEach(name => {
            const option = document.createElement('option');
            option.value = name;
            option.textContent = name;
            dropdown.appendChild(option);
        });

        // Add "Add new name..." option
        const addNewOption = document.createElement('option');
        addNewOption.value = '__ADD_NEW__';
        addNewOption.textContent = '+ Add new name...';
        dropdown.appendChild(addNewOption);

        // Restore previous selection if it exists
        if (currentValue && currentValue !== '__ADD_NEW__') {
            dropdown.value = currentValue;
        }
    });
}

// Function to populate profile image dropdowns for a specific dropdown
function populateProfileImageDropdown(dropdown) {
    const currentValue = dropdown.value;
    // Clear existing options except "No Image"
    dropdown.innerHTML = '<option value="">No Image</option>';
    profileImages.forEach(image => {
        const option = document.createElement('option');
        option.value = image;
        option.textContent = image;
        dropdown.appendChild(option);
    });
    // Restore previous selection if it exists
    if (currentValue) {
        dropdown.value = currentValue;
    }
}

// Function to populate all profile image dropdowns
function populateProfileImageDropdowns() {
    const dropdowns = document.querySelectorAll('.participant-profile-image');
    dropdowns.forEach(dropdown => {
        populateProfileImageDropdown(dropdown);
    });
}

// Function to load question image paths from the images directory
function loadQuestionImages() {
    // This function would typically fetch image names from a server or a predefined list.
    // For this exercise, we'll hardcode the list of Bible-related images
    questionImages = [
        "42_Lk_08_20_RG.jpg",
        "42_Lk_08_21_RG.jpg",
        "42_Lk_08_23_RG.jpg",
        "42_Lk_08_24_RG.jpg",
        "42_Lk_08_29_RG.jpg",
        "42_Lk_08_37_RG.jpg",
        "42_Lk_08_38_RG.jpg",
        "42_Lk_08_39_RG.jpg"
    ];
}

// Function to populate the question image dropdown
function populateQuestionImageDropdown() {
    const dropdown = document.getElementById('questionImageSelect');
    if (dropdown) {
        // Clear existing options except "No Image"
        dropdown.innerHTML = '<option value="">No Image</option>';
        
        // Add options for each image
        questionImages.forEach(image => {
            const option = document.createElement('option');
            option.value = image;
            option.textContent = image;
            dropdown.appendChild(option);
        });
        
        // Add event listener for image selection change
        dropdown.addEventListener('change', updateQuestionImagePreview);
    }
}

// Function to update the question image preview
function updateQuestionImagePreview() {
    const dropdown = document.getElementById('questionImageSelect');
    const preview = document.getElementById('questionImagePreview');
    
    if (dropdown && preview) {
        const selectedImage = dropdown.value;
        
        if (selectedImage) {
            // Show the selected image
            preview.innerHTML = `<img src="images/${selectedImage}" alt="Question Image">`;
        } else {
            // Clear the preview if no image is selected
            preview.innerHTML = '';
        }
    }
}

// Save state before page unload (refresh/close)
window.addEventListener('beforeunload', () => {
    // Only save if a quiz is in progress
    if (quizInProgress) {
        saveQuizState();
    }
});

// Image Preview Modal Functions
function openImagePreviewModal(imagePath) {
    const modal = document.getElementById('imagePreviewModal');
    const img = document.getElementById('imagePreviewImg');

    if (modal && img) {
        img.src = `images/${imagePath}`;
        modal.classList.add('active');

        // Focus on close button for accessibility
        const closeBtn = document.getElementById('imagePreviewCloseBtn');
        if (closeBtn) {
            closeBtn.focus();
        }
    }
}

function closeImagePreviewModal() {
    const modal = document.getElementById('imagePreviewModal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// ===== ANAGRAM FUNCTIONS =====

// Display anagram puzzle
function displayAnagram(anagramIndex) {
    const round = rounds[currentQuizState.currentRoundIndex];
    const anagram = round.anagrams[anagramIndex];

    // Update anagram state
    currentQuizState.anagramState.currentAnagramIndex = anagramIndex;
    currentQuizState.anagramState.clickedLetters = [];
    currentQuizState.anagramState.correctSequence = [...anagram.correctSequence];
    currentQuizState.anagramState.anagramCompleted = false;
    currentQuizState.anagramState.firstAttempt = true;

    // Show anagram container and hide question container
    document.getElementById('questionContainer').classList.add('hidden');
    document.getElementById('anagramContainer').classList.remove('hidden');

    // Update counter display
    document.getElementById('questionCounter').classList.add('hidden');
    document.getElementById('anagramCounter').classList.remove('hidden');
    document.getElementById('currentAnagram').textContent = anagramIndex + 1;
    document.getElementById('totalAnagrams').textContent = round.anagrams.length;

    // Create word display
    createWordDisplay(anagram);

    // Create random letters
    createRandomLetters(anagram);

    console.log('Displaying anagram:', anagram.word);
}

// Create word display with letter boxes
function createWordDisplay(anagram) {
    const wordDisplay = document.getElementById('anagramWordDisplay');
    wordDisplay.innerHTML = '';

    const word = anagram.word;
    const hintLetters = anagram.hintLetters;

    for (let i = 1; i <= word.length; i++) {
        const letterBox = document.createElement('div');
        letterBox.className = 'anagram-letter-box';
        letterBox.dataset.position = i;

        // Check if this position has a hint letter
        if (hintLetters[i]) {
            letterBox.textContent = hintLetters[i];
            letterBox.classList.add('hint');
        }

        wordDisplay.appendChild(letterBox);
    }
}

// Create random letters for clicking
function createRandomLetters(anagram) {
    const randomLettersContainer = document.getElementById('anagramRandomLettersDisplay');
    randomLettersContainer.innerHTML = '';

    // Use custom available letters order if provided, otherwise shuffle the random letters
    let lettersToDisplay;
    if (anagram.availableLettersOrder && anagram.availableLettersOrder.length > 0) {
        lettersToDisplay = [...anagram.availableLettersOrder];
    } else {
        // Fallback to shuffling for backward compatibility
        lettersToDisplay = [...anagram.randomLetters].sort(() => Math.random() - 0.5);
    }

    lettersToDisplay.forEach((letter) => {
        const letterElement = document.createElement('div');
        letterElement.className = 'anagram-random-letter';
        letterElement.textContent = letter;
        letterElement.dataset.letter = letter;
        letterElement.dataset.originalIndex = anagram.randomLetters.indexOf(letter);

        letterElement.addEventListener('click', () => handleLetterClick(letterElement, letter));

        randomLettersContainer.appendChild(letterElement);
    });
}

// Handle letter click
function handleLetterClick(letterElement, letter) {
    // Don't allow clicking if already clicked or anagram completed
    if (letterElement.classList.contains('clicked') || currentQuizState.anagramState.anagramCompleted) {
        return;
    }

    // Add debouncing to prevent rapid clicking issues
    if (letterElement.classList.contains('processing')) {
        return;
    }

    // Mark as processing to prevent rapid clicks
    letterElement.classList.add('processing');

    const correctSequence = currentQuizState.anagramState.correctSequence;
    const clickedLetters = currentQuizState.anagramState.clickedLetters;
    const expectedLetter = correctSequence[clickedLetters.length];

    if (letter === expectedLetter) {
        // Correct letter clicked
        handleCorrectLetterClick(letterElement, letter);
    } else {
        // Incorrect letter clicked
        handleIncorrectLetterClick(letterElement, letter);

        // Remove processing class for incorrect clicks so they can try again
        setTimeout(() => {
            letterElement.classList.remove('processing');
        }, 300);
    }
}

// Handle correct letter click
function handleCorrectLetterClick(letterElement, letter) {
    // Play correct audio
    wooshAudio.currentTime = 0;
    wooshAudio.play().catch(error => console.error('Error playing correct audio:', error));

    // Add to clicked letters
    currentQuizState.anagramState.clickedLetters.push(letter);

    // Mark letter as clicked
    letterElement.classList.add('clicked', 'correct-click');

    // Find the next empty position in the word
    const nextPosition = findNextEmptyPosition();
    if (nextPosition) {
        // IMMEDIATELY mark the position as filled to prevent race conditions
        nextPosition.classList.add('filled');
        nextPosition.textContent = letter;

        // Animate letter moving to position (visual effect only)
        animateLetterToPosition(letterElement, nextPosition, letter);
    }

    // Check if anagram is complete
    if (currentQuizState.anagramState.clickedLetters.length === currentQuizState.anagramState.correctSequence.length) {
        setTimeout(() => {
            completeAnagram();
        }, 800); // Wait for animation to complete
    }
}

// Handle incorrect letter click
function handleIncorrectLetterClick(letterElement, letter) {
    // Play wrong audio
    wrongAudio.currentTime = 0;
    wrongAudio.play().catch(error => console.error('Error playing wrong audio:', error));

    // Mark as first attempt failed (no points)
    currentQuizState.anagramState.firstAttempt = false;

    // Add incorrect click animation
    letterElement.classList.add('incorrect-click');

    // Remove animation class after animation completes
    setTimeout(() => {
        letterElement.classList.remove('incorrect-click');
    }, 600);
}

// Find next empty position in word display
function findNextEmptyPosition() {
    const round = rounds[currentQuizState.currentRoundIndex];
    const anagram = round.anagrams[currentQuizState.anagramState.currentAnagramIndex];

    // Find the first empty position (not filled and not a hint)
    for (let i = 1; i <= anagram.word.length; i++) {
        const letterBox = document.querySelector(`[data-position="${i}"]`);
        if (letterBox && !letterBox.classList.contains('filled') && !letterBox.classList.contains('hint')) {
            return letterBox;
        }
    }
    return null;
}

// Animate letter moving to position (visual effect only - letter is already placed)
function animateLetterToPosition(letterElement, targetBox, letter) {
    // Create a clone for animation
    const clone = letterElement.cloneNode(true);
    clone.classList.add('anagram-letter-move');

    // Position clone at original location
    const rect = letterElement.getBoundingClientRect();
    clone.style.position = 'fixed';
    clone.style.left = rect.left + 'px';
    clone.style.top = rect.top + 'px';
    clone.style.zIndex = '1000';

    document.body.appendChild(clone);

    // Animate to target position
    const targetRect = targetBox.getBoundingClientRect();

    setTimeout(() => {
        clone.style.left = targetRect.left + 'px';
        clone.style.top = targetRect.top + 'px';
        clone.style.transform = 'scale(1)';
    }, 50);

    // After animation, just remove clone (letter is already placed)
    setTimeout(() => {
        if (document.body.contains(clone)) {
            document.body.removeChild(clone);
        }
    }, 800);
}

// Complete anagram
function completeAnagram() {
    currentQuizState.anagramState.anagramCompleted = true;

    // Award points if first attempt
    if (currentQuizState.anagramState.firstAttempt) {
        const pointsToAdd = currentSettings.pointsPerCorrectAnswer || 1;
        currentQuizState.score[currentQuizState.currentTeamIndex] += pointsToAdd;
        updateTeamScoreDisplay();
        saveQuizState();

        // Show celebration animation for first attempt correct answers
        showCelebrationAnimation(currentQuizState.currentTeamIndex, pointsToAdd);
    } else {
        // For subsequent attempts, just enable the next question button
        const nextBtn = document.getElementById('nextQuestionBtn');
        nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next Question';
        nextBtn.classList.remove('pause-btn');
        nextBtn.disabled = false;
        nextBtn.style.display = 'inline-block';

        // Remove any existing click event listeners
        nextBtn.replaceWith(nextBtn.cloneNode(true));

        // Get the fresh button reference after cloning
        const freshNextBtn = document.getElementById('nextQuestionBtn');

        // Add the next question functionality
        freshNextBtn.addEventListener('click', nextQuestion);
    }

    console.log('Anagram completed! First attempt:', currentQuizState.anagramState.firstAttempt);
}

// Image Preview Modal Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    const modal = document.getElementById('imagePreviewModal');
    const closeBtn = document.getElementById('imagePreviewCloseBtn');
    const overlay = document.getElementById('imagePreviewOverlay');

    // Close modal when clicking close button
    if (closeBtn) {
        closeBtn.addEventListener('click', closeImagePreviewModal);
    }

    // Close modal when clicking overlay
    if (overlay) {
        overlay.addEventListener('click', closeImagePreviewModal);
    }

    // Close modal when pressing Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modal && modal.classList.contains('active')) {
            closeImagePreviewModal();
        }
    });
});