{"rounds": [{"name": "First Round", "questions": [{"text": "Who was the first man created according to the Bible?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 0, "image": "42_Lk_08_29_RG.jpg"}, {"text": "How many days did it take God to create the world according to Genesis?", "options": ["3 days", "6 days", "7 days", "40 days"], "correctOption": 1}, {"text": "Who built the ark according to the Bible?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 2}, {"text": "Which of these is NOT one of the Ten Commandments?", "options": ["Do not steal", "Love your neighbor as yourself", "Do not commit adultery", "Do not bear false witness"], "correctOption": 1}, {"text": "Who led the Israelites out of Egypt?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 1}, {"text": "Who built the ark?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 2}]}, {"name": "Second Round", "questions": [{"text": "How many days and nights did it rain during the Great Flood?", "options": ["7 days and 7 nights", "20 days and 20 nights", "40 days and 40 nights", "100 days and 100 nights"], "correctOption": 2}, {"text": "What was the first plague God sent upon Egypt?", "options": ["Frogs", "Gnats", "Water turning to blood", "Boils"], "correctOption": 2}, {"text": "Who led the Israelites out of Egypt?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 1}, {"text": "What city's walls fell after the Israelites marched around it?", "options": ["Bethlehem", "Jerusalem", "Jericho", "Babylon"], "correctOption": 2}, {"text": "Who was known for his incredible strength, which came from his long hair?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "Solomon"], "correctOption": 1}, {"text": "What was the name of the giant that <PERSON> defeated?", "options": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Canaanite", "Nebuchadnezzar"], "correctOption": 0}]}, {"name": "Third Round", "questions": [{"text": "How many books are in the New Testament?", "options": ["39", "27", "66", "12"], "correctOption": 1}, {"text": "What is the shortest verse in the Bible?", "options": ["<PERSON> 11:35 (\"<PERSON> wept.\")", "1 Thessalonians 5:16 (\"Rejoice always.\")", "Psalm 118:24 (\"This is the day the Lord has made.\")", "Luke 1:37 (\"For nothing will be impossible with <PERSON>.\")"], "correctOption": 0}, {"text": "Which disciple betrayed <PERSON>?", "options": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "correctOption": 2}, {"text": "Who was swallowed by a great fish after trying to flee from <PERSON>'s command?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 1}, {"text": "Which mountain is traditionally believed to be the place where <PERSON> received the Ten Commandments?", "options": ["Mount Zion", "Mount Carmel", "Mount Sinai", "Mount of Olives"], "correctOption": 2}, {"text": "In the New Testament, who wrote most of the epistles (letters)?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 3}]}, {"name": "Round Four", "questions": [{"text": "What do You See in this image?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 1, "image": "42_Lk_08_20_RG.jpg"}, {"text": "Tell me what did you see here", "options": ["Pig", "Cow", "Goa<PERSON>", "Lion"], "correctOption": 0, "image": "42_Lk_08_23_RG.jpg"}, {"text": "In the New Testament, who wrote most of the epistles (letters)?", "options": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "correctOption": 3, "image": ""}]}, {"name": "Anagram1", "type": "anagrams", "anagrams": [{"word": "PAUL", "hintLetters": {"3": "U"}, "randomLetters": ["P", "A", "L"], "correctSequence": ["P", "A", "L"]}, {"word": "CORINTH", "hintLetters": {"5": "N", "7": "H"}, "randomLetters": ["C", "O", "R", "I", "T"], "correctSequence": ["C", "O", "R", "I", "T"]}, {"word": "BIBLE", "hintLetters": {"5": "E"}, "randomLetters": ["B", "I", "B", "L"], "correctSequence": ["B", "I", "B", "L"]}]}], "teams": [{"name": "<PERSON>", "backgroundColor": "", "participants": [{"name": "<PERSON>", "profileImage": "APC Military Police Vehicle  Physics pack controller  available on the asset store 3.webp"}, {"name": "<PERSON>", "profileImage": "Armor Police Vehicle V221 game asset for Unity 2048x2048.webp"}]}, {"name": "<PERSON><PERSON><PERSON>", "backgroundColor": "", "participants": [{"name": "<PERSON>", "profileImage": "New Update Terrain Land Ponds and Lake Summer Pack 3d game assets for Unity 2.webp"}, {"name": "<PERSON>", "profileImage": "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 09.webp"}]}, {"name": "Beryl", "backgroundColor": "", "participants": [{"name": "<PERSON>", "profileImage": "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 13.webp"}, {"name": "<PERSON>", "profileImage": "APC Military Police Vehicle  Physics pack controller  available on the asset store 26.webp"}]}, {"name": "Diamond", "backgroundColor": "", "participants": [{"name": "Samborlang", "profileImage": "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 13.webp"}, {"name": "<PERSON>", "profileImage": "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 07.webp"}]}, {"name": "Stone 5", "backgroundColor": "", "participants": [{"name": "<PERSON>", "profileImage": "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 09.webp"}, {"name": "<PERSON>", "profileImage": "APC Military Police Vehicle  Physics pack controller  available on the asset store 3.webp"}]}, {"name": "Stone 6", "backgroundColor": "", "participants": [{"name": "<PERSON>", "profileImage": "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 09.webp"}, {"name": "<PERSON>", "profileImage": "Off-Road Vehicle Pack + Car Controller Script 3d game asset for Unity 17.webp"}]}], "settings": {"quizTitle": "Bible Quiz Competition", "timerDuration": 30, "pointsPerCorrectAnswer": 100, "shuffleQuestions": false, "titleAlignment": "left", "titleSize": "2", "questionAlignment": "left", "questionSize": "1.5", "timerSize": "1"}, "participantNames": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sam", "Samborlang", "<PERSON>", "<PERSON>"], "questionImages": ["42_Lk_08_20_RG.jpg", "42_Lk_08_21_RG.jpg", "42_Lk_08_23_RG.jpg", "42_Lk_08_24_RG.jpg", "42_Lk_08_29_RG.jpg", "42_Lk_08_37_RG.jpg", "42_Lk_08_38_RG.jpg", "42_Lk_08_39_RG.jpg"], "version": "1.1", "exportDate": "2025-06-24T15:05:29.510Z"}