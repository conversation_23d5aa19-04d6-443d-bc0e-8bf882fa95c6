<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bible Quiz Features Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
        #testResults {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
</head>
<body>
    <h1>Bible Quiz Features Test</h1>
    
    <div class="test-section">
        <h2>🎵 Audio Features Test</h2>
        <p>Test the fireworks sound that should play with confetti animations:</p>
        <button class="test-button" onclick="testFireworksSound()">Test Fireworks Sound</button>
        <button class="test-button" onclick="testTimerSounds()">Test Timer Sounds</button>
    </div>
    
    <div class="test-section">
        <h2>🎉 Confetti Animation Test</h2>
        <p>Test confetti animations with fireworks sound:</p>
        <button class="test-button" onclick="testConfettiWithSound()">Test Confetti + Sound</button>
        <button class="test-button" onclick="testScoreConfetti()">Test Score Confetti</button>
    </div>
    
    <div class="test-section">
        <h2>⏱️ Timer Settings Test</h2>
        <p>Test round-specific timer settings:</p>
        <div>
            <label>Timer Duration: </label>
            <input type="number" id="testTimerDuration" value="15" min="5" max="120">
            <label>Timer Sound: </label>
            <select id="testTimerSound">
                <option value="clock_timer.mp3">Clock Timer</option>
                <option value="clock_40sec.mp3">Clock 40sec</option>
                <option value="woosh.mp3">Woosh</option>
            </select>
            <button class="test-button" onclick="testTimerSettings()">Test Timer</button>
        </div>
        <div id="timerDisplay" style="margin-top: 10px; font-size: 24px; font-weight: bold;"></div>
    </div>
    
    <div class="test-section">
        <h2>🔧 Integration Test</h2>
        <p>Open the main application to test the full integration:</p>
        <button class="test-button" onclick="openMainApp()">Open Main Bible Quiz App</button>
    </div>
    
    <div id="testResults"></div>

    <script>
        // Audio elements for testing
        const fireworksAudio = new Audio('audio/fireworks.mp3');
        let timerAudio = new Audio('audio/clock_timer.mp3');
        let testTimer = null;
        let testTimeLeft = 0;
        
        function logResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }
        
        function testFireworksSound() {
            logResult('Testing fireworks sound...', 'info');
            fireworksAudio.currentTime = 0;
            fireworksAudio.play().then(() => {
                logResult('✓ Fireworks sound played successfully!', 'success');
            }).catch(error => {
                logResult('✗ Error playing fireworks sound: ' + error.message, 'error');
            });
        }
        
        function testTimerSounds() {
            const sounds = ['clock_timer.mp3', 'clock_40sec.mp3', 'woosh.mp3'];
            let index = 0;
            
            function playNext() {
                if (index < sounds.length) {
                    const sound = sounds[index];
                    logResult(`Testing timer sound: ${sound}`, 'info');
                    const audio = new Audio('audio/' + sound);
                    audio.play().then(() => {
                        logResult(`✓ ${sound} played successfully!`, 'success');
                        index++;
                        setTimeout(playNext, 2000);
                    }).catch(error => {
                        logResult(`✗ Error playing ${sound}: ${error.message}`, 'error');
                        index++;
                        setTimeout(playNext, 1000);
                    });
                }
            }
            
            playNext();
        }
        
        function testConfettiWithSound() {
            logResult('Testing confetti with fireworks sound...', 'info');
            
            // Play fireworks sound
            fireworksAudio.currentTime = 0;
            fireworksAudio.play().catch(error => {
                logResult('Warning: Could not play fireworks sound: ' + error.message, 'error');
            });
            
            // Trigger confetti
            if (typeof confetti !== 'undefined') {
                confetti({
                    particleCount: 100,
                    spread: 70,
                    origin: { y: 0.6 }
                });
                logResult('✓ Confetti animation triggered with sound!', 'success');
            } else {
                logResult('✗ Confetti library not loaded', 'error');
            }
        }
        
        function testScoreConfetti() {
            logResult('Testing score confetti with fireworks sound...', 'info');
            
            // Play fireworks sound
            fireworksAudio.currentTime = 0;
            fireworksAudio.play().catch(error => {
                logResult('Warning: Could not play fireworks sound: ' + error.message, 'error');
            });
            
            // Multiple confetti bursts
            const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
            
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    confetti({
                        particleCount: 100,
                        spread: 70,
                        origin: { y: 0.6 },
                        colors: colors
                    });
                }, i * 300);
            }
            
            logResult('✓ Score confetti animation triggered with sound!', 'success');
        }
        
        function testTimerSettings() {
            const duration = parseInt(document.getElementById('testTimerDuration').value);
            const sound = document.getElementById('testTimerSound').value;
            
            logResult(`Testing timer: ${duration}s with sound: ${sound}`, 'info');
            
            // Stop any existing timer
            if (testTimer) {
                clearInterval(testTimer);
            }
            
            // Update timer audio
            timerAudio.src = 'audio/' + sound;
            timerAudio.load();
            
            // Start test timer
            testTimeLeft = duration;
            const display = document.getElementById('timerDisplay');
            display.textContent = testTimeLeft;
            
            // Play timer sound
            timerAudio.currentTime = 0;
            timerAudio.play().catch(error => {
                logResult('Warning: Could not play timer sound: ' + error.message, 'error');
            });
            
            testTimer = setInterval(() => {
                testTimeLeft--;
                display.textContent = testTimeLeft;
                
                if (testTimeLeft <= 0) {
                    clearInterval(testTimer);
                    display.textContent = "Time's Up!";
                    logResult('✓ Timer test completed successfully!', 'success');
                }
            }, 1000);
        }
        
        function openMainApp() {
            window.open('index.html', '_blank');
            logResult('✓ Main application opened in new tab', 'success');
        }
        
        // Initialize test results
        logResult('Test page loaded successfully. Ready to test features!', 'success');
    </script>
</body>
</html>
