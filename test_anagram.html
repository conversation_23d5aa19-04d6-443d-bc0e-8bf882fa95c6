<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Anagram Interface</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>Test New Anagram Interface</h1>
        
        <!-- Test Modal -->
        <div class="modal active" id="addAnagramModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="anagramModalTitle">Add New Anagram</h2>
                    <span class="close-btn" id="closeAnagramModalBtn">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="anagramWordInput">Word:</label>
                        <input type="text" id="anagramWordInput" placeholder="Enter the complete word (e.g., HOUSE)" style="text-transform: uppercase;">
                    </div>
                    
                    <!-- Hint Pattern Section -->
                    <div class="form-group">
                        <label>Hint Pattern:</label>
                        <p class="hint-pattern-instruction">Click on letters to toggle between hint letter and blank (_)</p>
                        <div class="hint-pattern-container" id="hintPatternContainer">
                            <!-- Hint pattern letters will be generated dynamically -->
                        </div>
                    </div>
                    
                    <!-- Scrambled Letters Section -->
                    <div class="form-group">
                        <label>Scrambled Letters:</label>
                        <p class="scrambled-letters-instruction">Drag letters to arrange them as you want them to appear in the game. You can add extra letters to make it harder!</p>
                        <div class="scrambled-letters-container" id="scrambledLettersContainer">
                            <!-- Available letters will be generated dynamically -->
                        </div>
                        <div class="scrambled-letters-actions">
                            <button type="button" id="addExtraLetterBtn" class="btn small"><i class="fas fa-plus"></i> Add Extra Letter</button>
                            <button type="button" id="shuffleLettersBtn" class="btn small"><i class="fas fa-random"></i> Shuffle</button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="testLogicBtn" class="btn" style="background: #28a745; color: white; margin-right: 10px;">Test Logic</button>
                    <button id="saveAnagramBtn" class="btn skyblue">Save Anagram</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Minimal test script
        const rounds = [{ name: "Test Round", type: "anagrams", anagrams: [] }];
        
        // Include only the necessary functions for testing
        function handleWordInputChange() {
            const wordInput = document.getElementById('anagramWordInput');
            if (!wordInput) return;
            
            const word = wordInput.value.trim().toUpperCase();
            if (word) {
                generateHintPatternFromWord(word);
                updateScrambledLettersFromHintPattern();
            } else {
                clearHintPatternContainer();
                clearScrambledLettersContainer();
            }
        }

        function clearHintPatternContainer() {
            const container = document.getElementById('hintPatternContainer');
            if (container) {
                container.innerHTML = '';
            }
        }

        function clearScrambledLettersContainer() {
            const container = document.getElementById('scrambledLettersContainer');
            if (container) {
                container.innerHTML = '';
            }
        }

        function generateHintPatternFromWord(word, existingHints = {}) {
            const container = document.getElementById('hintPatternContainer');
            if (!container) return;
            
            container.innerHTML = '';
            
            for (let i = 0; i < word.length; i++) {
                const letter = word[i];
                const position = i + 1;
                
                const letterElement = document.createElement('div');
                letterElement.className = 'hint-pattern-letter';
                letterElement.textContent = letter;
                letterElement.dataset.position = position;
                letterElement.dataset.letter = letter;
                
                if (existingHints[position]) {
                    letterElement.classList.add('hint');
                } else {
                    letterElement.classList.add('blank');
                    letterElement.textContent = '_';
                }
                
                letterElement.addEventListener('click', toggleHintLetter);
                container.appendChild(letterElement);
            }
        }

        function toggleHintLetter(event) {
            const letterElement = event.target;
            const letter = letterElement.dataset.letter;
            
            if (letterElement.classList.contains('hint')) {
                letterElement.classList.remove('hint');
                letterElement.classList.add('blank');
                letterElement.textContent = '_';
            } else {
                letterElement.classList.remove('blank');
                letterElement.classList.add('hint');
                letterElement.textContent = letter;
            }
            
            updateScrambledLettersFromHintPattern();
        }

        function updateScrambledLettersFromHintPattern() {
            const hintPatternContainer = document.getElementById('hintPatternContainer');
            const scrambledContainer = document.getElementById('scrambledLettersContainer');
            if (!hintPatternContainer || !scrambledContainer) return;

            // Get current extra letters (preserve them)
            const currentExtraLetters = Array.from(scrambledContainer.querySelectorAll('.scrambled-letter.extra'))
                .map(el => ({ letter: el.dataset.letter, element: el }));

            // Get blank letters from hint pattern
            const blankLetters = [];
            const hintLetters = hintPatternContainer.querySelectorAll('.hint-pattern-letter.blank');

            hintLetters.forEach(letterElement => {
                blankLetters.push(letterElement.dataset.letter);
            });

            // Clear container and regenerate with blank letters
            scrambledContainer.innerHTML = '';

            // Add blank letters first
            blankLetters.forEach(letter => {
                createScrambledLetter(letter, false, scrambledContainer);
            });

            // Re-add extra letters
            currentExtraLetters.forEach(({ letter }) => {
                createScrambledLetter(letter, true, scrambledContainer);
            });
        }

        function generateScrambledLettersFromArray(letters) {
            const container = document.getElementById('scrambledLettersContainer');
            if (!container) return;
            
            container.innerHTML = '';
            
            letters.forEach((letter) => {
                createScrambledLetter(letter, false, container);
            });
        }

        function createScrambledLetter(letter, isExtra = false, container = null) {
            if (!container) {
                container = document.getElementById('scrambledLettersContainer');
            }
            if (!container) return null;
            
            const letterElement = document.createElement('div');
            letterElement.className = 'scrambled-letter';
            letterElement.textContent = letter;
            letterElement.dataset.letter = letter;
            letterElement.draggable = true;
            
            if (isExtra) {
                letterElement.classList.add('extra');
                
                const removeBtn = document.createElement('button');
                removeBtn.className = 'remove-letter';
                removeBtn.innerHTML = '×';
                removeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    letterElement.remove();
                });
                letterElement.appendChild(removeBtn);
            }
            
            letterElement.addEventListener('dragstart', handleDragStart);
            letterElement.addEventListener('dragend', handleDragEnd);
            
            container.appendChild(letterElement);
            return letterElement;
        }

        function handleDragStart(event) {
            event.dataTransfer.setData('text/plain', event.target.dataset.letter);
            event.target.classList.add('dragging');
        }

        function handleDragEnd(event) {
            event.target.classList.remove('dragging');
        }

        function addExtraLetter() {
            const letter = prompt('Enter a letter to add:');
            if (letter && letter.length === 1 && /[A-Z]/i.test(letter)) {
                const container = document.getElementById('scrambledLettersContainer');
                createScrambledLetter(letter.toUpperCase(), true, container);
            }
        }

        function shuffleScrambledLetters() {
            const container = document.getElementById('scrambledLettersContainer');
            if (!container) return;
            
            const letters = Array.from(container.children);
            const shuffled = letters.sort(() => Math.random() - 0.5);
            
            container.innerHTML = '';
            shuffled.forEach(letter => container.appendChild(letter));
        }

        // Test the logic
        function testAnagramLogic() {
            const wordInput = document.getElementById('anagramWordInput');
            const hintPatternContainer = document.getElementById('hintPatternContainer');
            const scrambledContainer = document.getElementById('scrambledLettersContainer');

            if (!wordInput.value) {
                alert('Please enter a word first!');
                return;
            }

            const word = wordInput.value.trim().toUpperCase();

            // Get hint letters
            const hintLetters = {};
            const hintPatternLetters = hintPatternContainer.querySelectorAll('.hint-pattern-letter.hint');
            hintPatternLetters.forEach(letterElement => {
                const position = parseInt(letterElement.dataset.position);
                const letter = letterElement.dataset.letter;
                hintLetters[position] = letter;
            });

            // Get scrambled letters
            const scrambledLetterElements = scrambledContainer.querySelectorAll('.scrambled-letter');
            const availableLettersOrder = Array.from(scrambledLetterElements).map(el => el.dataset.letter);

            // Create correctSequence: letters in word order for blank positions only
            const correctSequence = [];
            const randomLetters = [];

            for (let i = 1; i <= word.length; i++) {
                if (!hintLetters[i]) {
                    const letter = word[i - 1];
                    correctSequence.push(letter);
                    randomLetters.push(letter);
                }
            }

            console.log('=== ANAGRAM LOGIC TEST ===');
            console.log('Word:', word);
            console.log('Hint Letters:', hintLetters);
            console.log('Available Letters Order:', availableLettersOrder);
            console.log('Random Letters (needed):', randomLetters);
            console.log('Correct Sequence:', correctSequence);

            alert(`ANAGRAM LOGIC TEST:\n\nWord: ${word}\nHint Letters: ${JSON.stringify(hintLetters)}\nAvailable Letters: [${availableLettersOrder.join(', ')}]\nCorrect Sequence: [${correctSequence.join(', ')}]\n\nCheck console for details!`);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            const wordInput = document.getElementById('anagramWordInput');
            if (wordInput) {
                wordInput.addEventListener('input', handleWordInputChange);
            }
            
            const testLogicBtn = document.getElementById('testLogicBtn');
            if (testLogicBtn) {
                testLogicBtn.addEventListener('click', testAnagramLogic);
            }

            const addExtraLetterBtn = document.getElementById('addExtraLetterBtn');
            if (addExtraLetterBtn) {
                addExtraLetterBtn.addEventListener('click', addExtraLetter);
            }
            
            const shuffleLettersBtn = document.getElementById('shuffleLettersBtn');
            if (shuffleLettersBtn) {
                shuffleLettersBtn.addEventListener('click', shuffleScrambledLetters);
            }
            
            const scrambledContainer = document.getElementById('scrambledLettersContainer');
            if (scrambledContainer) {
                scrambledContainer.addEventListener('dragover', function(event) {
                    event.preventDefault();
                    event.currentTarget.classList.add('drag-over');
                });
                
                scrambledContainer.addEventListener('drop', function(event) {
                    event.preventDefault();
                    event.currentTarget.classList.remove('drag-over');
                });
                
                scrambledContainer.addEventListener('dragleave', function(event) {
                    event.currentTarget.classList.remove('drag-over');
                });
            }
        });
    </script>
</body>
</html>
